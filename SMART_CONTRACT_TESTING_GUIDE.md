# Smart Contract Integration Testing Guide

## Overview

This guide covers the comprehensive testing strategy for the smart contract integration changes made to the DashboardPage and related hooks. The tests validate that all mock data has been successfully replaced with real on-chain data and that the dashboard displays accurate information from all 5 smart contracts.

## Test Structure

### 📁 Test Files Created

```
frontend/src/
├── hooks/__tests__/
│   ├── useReferralManager.test.ts      # NEW - Referral data calculation tests
│   ├── useUSDTToken.test.ts            # NEW - USDT balance and precision tests
│   ├── useLPToken.test.ts              # NEW - LP token balance tests
│   ├── useUserPackages.test.ts         # NEW - Package data fetching tests
│   ├── useVestingVault.test.ts         # UPDATED - Wagmi query fixes tests
│   ├── integration.test.ts             # NEW - Cross-hook integration tests
│   ├── usePackageManager.test.ts       # EXISTING - Regression tests
│   └── useOnChainPackages.test.ts      # EXISTING - Regression tests
├── pages/__tests__/
│   └── DashboardPage.test.tsx          # NEW - Component integration tests
└── test/
    └── runSmartContractTests.ts        # NEW - Test runner script
```

## Test Categories

### 🧪 Unit Tests

#### 1. Hook Testing (`useReferralManager.test.ts`)
- **Purpose**: Test referral data calculation and error handling
- **Key Tests**:
  - Referral earnings calculation from ShareToken balance
  - Referral count estimation (150 tokens per referral)
  - Zero balance handling
  - Contract address validation
  - Error handling for undefined data

#### 2. USDT Token Testing (`useUSDTToken.test.ts`)
- **Purpose**: Test USDT balance fetching with 6-decimal precision
- **Key Tests**:
  - 6-decimal precision formatting
  - Balance and allowance fetching
  - Large amount handling (1M+ USDT)
  - Small amount handling (fractional USDT)
  - PackageManager allowance checking

#### 3. LP Token Testing (`useLPToken.test.ts`)
- **Purpose**: Test LP token balance and metadata retrieval
- **Key Tests**:
  - 18-decimal precision formatting
  - Token metadata (name, symbol) fetching
  - Balance and total supply queries
  - Default value handling
  - Refetch functionality

#### 4. User Packages Testing (`useUserPackages.test.ts`)
- **Purpose**: Test package data fetching and calculations
- **Key Tests**:
  - Package details formatting from contract data
  - Total invested calculation
  - Average APY calculation
  - Purchase date generation
  - Error handling for failed package fetches

#### 5. Vesting Vault Testing (`useVestingVault.test.ts`)
- **Purpose**: Test updated wagmi query patterns
- **Key Tests**:
  - Wagmi `query.enabled` parameter usage
  - BigInt type checking and formatting
  - Claim functionality
  - Data refresh after successful claims
  - Large token amount handling

### 🔗 Integration Tests

#### 1. Component Integration (`DashboardPage.test.tsx`)
- **Purpose**: Test complete DashboardPage with real on-chain data
- **Key Tests**:
  - Wallet connection state handling
  - Real-time data display from all hooks
  - Asset allocation calculations
  - Transaction history generation
  - Claim functionality integration
  - Navigation and user interactions

#### 2. Cross-Hook Dependencies (`integration.test.ts`)
- **Purpose**: Test how hooks work together
- **Key Tests**:
  - Data consistency across multiple hooks
  - Asset allocation calculations using multiple token balances
  - USDT precision handling across all hooks
  - Error handling integration
  - Real-time data synchronization
  - Contract address validation

## Test Data Patterns

### 🎯 Realistic Contract Data

```typescript
// USDT amounts (6 decimals)
const usdtAmount = BigInt('1000000000'); // 1,000 USDT

// Token amounts (18 decimals)
const tokenAmount = BigInt('1000000000000000000000'); // 1,000 tokens

// Package details
const packageData = {
  entryUSDT: BigInt('5000000000'), // 5,000 USDT
  priceBps: 1200, // 12% APY
  vestSplitBps: 5000, // 50% vesting
  referralRateBps: 500, // 5% referral
  exists: true,
};
```

### 🔧 Mock Patterns

```typescript
// Wagmi hook mocking
mockUseReadContract.mockReturnValue({
  data: BigInt('1000000000000000000000'),
  refetch: vi.fn(),
});

// Contract address mocking
vi.mock('../../contracts', () => ({
  getUSDTContract: vi.fn(() => ({
    address: '0xUSDTToken',
    abi: [],
  })),
}));
```

## Running Tests

### 🚀 Quick Start

```bash
# Run all smart contract integration tests
npm run test:smart-contracts

# Run specific test suites
npm test -- src/hooks/__tests__/useReferralManager.test.ts
npm test -- src/pages/__tests__/DashboardPage.test.tsx
npm test -- src/hooks/__tests__/integration.test.ts

# Run with coverage
npm test -- --coverage
```

### 📊 Test Runner Script

```bash
# Use the comprehensive test runner
npx ts-node src/test/runSmartContractTests.ts
```

The test runner provides:
- ✅ Test file validation
- 📋 Detailed test suite information
- 🧪 Sequential test execution
- 📊 Coverage report generation
- 📈 Comprehensive summary

## Test Coverage Areas

### ✅ Validated Functionality

1. **Complete Smart Contract Integration**
   - All 5 contracts (VestingVault, ReferralManager, BlockCoopTestTether, PackageManager, LPToken)
   - Real-time on-chain data fetching
   - Proper contract address usage

2. **Data Accuracy**
   - USDT 6-decimal precision
   - Token 18-decimal precision
   - BigInt handling and formatting
   - Cross-hook data consistency

3. **User Interface**
   - Real-time balance display
   - Dynamic asset allocation
   - Transaction history from on-chain data
   - Claim functionality with proper feedback

4. **Error Handling**
   - Network errors
   - Missing data
   - Invalid responses
   - Loading states

5. **TypeScript Safety**
   - Interface compliance
   - Type checking
   - Proper error types

## Edge Cases Tested

### 🔍 Boundary Conditions

- **Zero Balances**: All hooks handle zero amounts gracefully
- **Large Amounts**: Support for millions of tokens/USDT
- **Fractional Amounts**: Proper decimal handling
- **Network Failures**: Graceful degradation
- **Missing Metadata**: Default value fallbacks
- **Disconnected Wallet**: Proper state handling

### ⚠️ Error Scenarios

- Contract call failures
- Invalid contract responses
- Network timeouts
- Malformed data
- Missing contract addresses

## Best Practices Implemented

### 🎯 Testing Standards

1. **Isolation**: Each test is independent
2. **Mocking**: External dependencies properly mocked
3. **Assertions**: Specific, meaningful test assertions
4. **Coverage**: Both success and failure scenarios
5. **Realism**: Test data matches actual contract structure

### 🔧 Mock Strategy

1. **Wagmi Hooks**: Comprehensive mocking with proper return types
2. **Contract Calls**: Realistic response simulation
3. **Error Handling**: Various error type simulation
4. **Loading States**: Async operation testing

## Continuous Integration

### 📋 Pre-deployment Checklist

- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Component tests pass
- [ ] Coverage > 80%
- [ ] No TypeScript errors
- [ ] All edge cases covered

### 🚀 Deployment Validation

1. Run full test suite
2. Verify coverage report
3. Check for any failing tests
4. Validate error handling
5. Confirm real contract integration

## Troubleshooting

### 🐛 Common Issues

1. **Mock Setup**: Ensure proper wagmi hook mocking
2. **BigInt Handling**: Use proper BigInt values in tests
3. **Async Operations**: Use `waitFor` for async tests
4. **Contract Addresses**: Verify mock contract addresses
5. **Type Safety**: Ensure proper TypeScript types

### 🔧 Debug Tips

- Use `console.log` in tests for debugging
- Check mock call counts and arguments
- Verify contract function names match
- Ensure proper decimal handling
- Test with realistic data amounts

## Conclusion

This comprehensive testing suite ensures that:

✅ **All mock data has been replaced with real on-chain data**  
✅ **Dashboard displays accurate information from all 5 smart contracts**  
✅ **Complete user journey is tested from wallet connection to data display**  
✅ **Proper handling of BigInt values and decimal precision**  
✅ **Error handling and edge cases are covered**  
✅ **TypeScript interfaces and type safety are validated**

The tests provide confidence that the smart contract integration is robust, accurate, and ready for production deployment.
