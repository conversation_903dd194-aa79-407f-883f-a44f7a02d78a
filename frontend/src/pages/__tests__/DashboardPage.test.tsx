import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { DashboardPage } from '../DashboardPage';

// Mock all the hooks
vi.mock('../../hooks/useWallet', () => ({
  useWallet: vi.fn(),
}));

vi.mock('../../hooks/useVestingVault', () => ({
  useVestingVault: vi.fn(),
}));

vi.mock('../../hooks/useReferralManager', () => ({
  useReferralManager: vi.fn(),
}));

vi.mock('../../hooks/useUSDTToken', () => ({
  useUSDTToken: vi.fn(),
}));

vi.mock('../../hooks/useLPToken', () => ({
  useLPToken: vi.fn(),
}));

vi.mock('../../hooks/useUserPackages', () => ({
  useUserPackages: vi.fn(),
}));

vi.mock('../../hooks/useShareToken', () => ({
  useShareToken: vi.fn(),
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock recharts
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: ({ children }: any) => <div data-testid="pie">{children}</div>,
  Cell: () => <div data-testid="cell" />,
}));

import { useWallet } from '../../hooks/useWallet';
import { useVestingVault } from '../../hooks/useVestingVault';
import { useReferralManager } from '../../hooks/useReferralManager';
import { useUSDTToken } from '../../hooks/useUSDTToken';
import { useLPToken } from '../../hooks/useLPToken';
import { useUserPackages } from '../../hooks/useUserPackages';
import { useShareToken } from '../../hooks/useShareToken';

const mockUseWallet = useWallet as any;
const mockUseVestingVault = useVestingVault as any;
const mockUseReferralManager = useReferralManager as any;
const mockUseUSDTToken = useUSDTToken as any;
const mockUseLPToken = useLPToken as any;
const mockUseUserPackages = useUserPackages as any;
const mockUseShareToken = useShareToken as any;

describe('DashboardPage', () => {
  const mockOnNavigate = vi.fn();
  const mockClaimVestedTokens = vi.fn();
  const mockRefetchClaimableAmount = vi.fn();
  const mockRefetchVestedAmount = vi.fn();

  const defaultMockUser = {
    id: '******************************************',
    address: '******************************************',
    balance: 1000,
    vestedTokens: 5000,
    totalPurchased: 10000,
    referralCode: '123456',
    referralCount: 5,
    isConnected: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    mockUseWallet.mockReturnValue({
      user: defaultMockUser,
      isConnected: true,
    });

    mockUseVestingVault.mockReturnValue({
      vestedAmount: '5000.0',
      claimableAmount: '1500.0',
      claimVestedTokens: mockClaimVestedTokens,
      isPending: false,
      isSuccess: false,
      refetchClaimableAmount: mockRefetchClaimableAmount,
      refetchVestedAmount: mockRefetchVestedAmount,
    });

    mockUseReferralManager.mockReturnValue({
      totalEarnings: '750.0',
      referralCount: 5,
    });

    mockUseUSDTToken.mockReturnValue({
      balance: '2500.0',
    });

    mockUseLPToken.mockReturnValue({
      balance: '1000.0',
    });

    mockUseUserPackages.mockReturnValue({
      userPackages: [
        {
          packageId: 0,
          name: 'Investment Package 1',
          entryAmount: '1000',
          apy: 12,
          vestingPercentage: 50,
          referralRate: 5,
          purchaseDate: '2024-01-15',
          status: 'active',
        },
        {
          packageId: 1,
          name: 'Investment Package 2',
          entryAmount: '2500',
          apy: 15,
          vestingPercentage: 60,
          referralRate: 3,
          purchaseDate: '2024-01-10',
          status: 'active',
        },
      ],
      totalInvested: 3500,
    });

    mockUseShareToken.mockReturnValue({
      balance: '10000.0',
    });
  });

  it('should render wallet connection prompt when not connected', () => {
    mockUseWallet.mockReturnValue({
      user: null,
      isConnected: false,
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Connect Your Wallet')).toBeInTheDocument();
    expect(screen.getByText('Please connect your wallet to view your dashboard and portfolio information.')).toBeInTheDocument();
    expect(screen.getByText('Go to Home Page')).toBeInTheDocument();
  });

  it('should render dashboard with real on-chain data when connected', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    // Check header
    expect(screen.getByText('Welcome back!')).toBeInTheDocument();
    expect(screen.getByText('Address: ******************************************')).toBeInTheDocument();

    // Check stats cards with real data
    expect(screen.getByText('USDT Balance')).toBeInTheDocument();
    expect(screen.getByText('$2,500')).toBeInTheDocument();
    
    expect(screen.getByText('Vested Tokens')).toBeInTheDocument();
    expect(screen.getByText('5,000 Tokens')).toBeInTheDocument();
    expect(screen.getByText('1,500 claimable')).toBeInTheDocument();
    
    expect(screen.getByText('Share Tokens')).toBeInTheDocument();
    expect(screen.getByText('10,000 Tokens')).toBeInTheDocument();
    expect(screen.getByText('$3,500 invested')).toBeInTheDocument();
    
    expect(screen.getByText('Referral Earnings')).toBeInTheDocument();
    expect(screen.getByText('750 Tokens')).toBeInTheDocument();
    expect(screen.getByText('5 referrals')).toBeInTheDocument();
  });

  it('should display portfolio performance chart', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Portfolio Performance')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
  });

  it('should display asset allocation with real data', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Asset Allocation')).toBeInTheDocument();
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
  });

  it('should display recent activity from real transaction data', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    expect(screen.getByText('Package Purchase')).toBeInTheDocument();
    expect(screen.getByText('Token Claim')).toBeInTheDocument();
    expect(screen.getByText('Referral Reward')).toBeInTheDocument();
  });

  it('should display vesting schedule with claim functionality', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('Vesting Schedule')).toBeInTheDocument();
    expect(screen.getByText('Available to Claim')).toBeInTheDocument();
    expect(screen.getByText('1,500 Tokens')).toBeInTheDocument();
    
    const claimButton = screen.getByText('Claim');
    expect(claimButton).toBeInTheDocument();
    expect(claimButton).not.toBeDisabled();
  });

  it('should handle claim button click', async () => {
    mockClaimVestedTokens.mockResolvedValue({ hash: '0xmockhash' });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    const claimButton = screen.getByText('Claim');
    fireEvent.click(claimButton);

    await waitFor(() => {
      expect(mockClaimVestedTokens).toHaveBeenCalled();
    });
  });

  it('should disable claim button when pending', () => {
    mockUseVestingVault.mockReturnValue({
      vestedAmount: '5000.0',
      claimableAmount: '1500.0',
      claimVestedTokens: mockClaimVestedTokens,
      isPending: true,
      isSuccess: false,
      refetchClaimableAmount: mockRefetchClaimableAmount,
      refetchVestedAmount: mockRefetchVestedAmount,
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    const claimButton = screen.getByText('Processing...');
    expect(claimButton).toBeDisabled();
  });

  it('should disable claim button when no claimable amount', () => {
    mockUseVestingVault.mockReturnValue({
      vestedAmount: '5000.0',
      claimableAmount: '0',
      claimVestedTokens: mockClaimVestedTokens,
      isPending: false,
      isSuccess: false,
      refetchClaimableAmount: mockRefetchClaimableAmount,
      refetchVestedAmount: mockRefetchVestedAmount,
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    const claimButton = screen.getByText('Claim');
    expect(claimButton).toBeDisabled();
  });

  it('should refresh data after successful claim', () => {
    mockUseVestingVault.mockReturnValue({
      vestedAmount: '5000.0',
      claimableAmount: '1500.0',
      claimVestedTokens: mockClaimVestedTokens,
      isPending: false,
      isSuccess: true,
      refetchClaimableAmount: mockRefetchClaimableAmount,
      refetchVestedAmount: mockRefetchVestedAmount,
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(mockRefetchClaimableAmount).toHaveBeenCalled();
    expect(mockRefetchVestedAmount).toHaveBeenCalled();
  });

  it('should handle navigation buttons', () => {
    render(<DashboardPage onNavigate={mockOnNavigate} />);

    const investMoreButton = screen.getByText('Invest More');
    fireEvent.click(investMoreButton);

    expect(mockOnNavigate).toHaveBeenCalledWith('purchase');
  });

  it('should calculate asset allocation correctly with zero LP tokens', () => {
    mockUseLPToken.mockReturnValue({
      balance: '0',
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    // Should still render allocation chart even with zero LP tokens
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
  });

  it('should handle zero balances gracefully', () => {
    mockUseUSDTToken.mockReturnValue({ balance: '0' });
    mockUseShareToken.mockReturnValue({ balance: '0' });
    mockUseLPToken.mockReturnValue({ balance: '0' });
    mockUseVestingVault.mockReturnValue({
      vestedAmount: '0',
      claimableAmount: '0',
      claimVestedTokens: mockClaimVestedTokens,
      isPending: false,
      isSuccess: false,
      refetchClaimableAmount: mockRefetchClaimableAmount,
      refetchVestedAmount: mockRefetchVestedAmount,
    });
    mockUseReferralManager.mockReturnValue({
      totalEarnings: '0',
      referralCount: 0,
    });

    render(<DashboardPage onNavigate={mockOnNavigate} />);

    expect(screen.getByText('$0')).toBeInTheDocument();
    expect(screen.getByText('0 Tokens')).toBeInTheDocument();
  });
});
