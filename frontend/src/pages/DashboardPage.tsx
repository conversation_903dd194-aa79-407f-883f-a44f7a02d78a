import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Wallet, 
  Clock, 
  Users, 
  Download,
  ArrowUpRight,
  Calendar,
  DollarSign
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON>Axis, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { useWallet } from '../hooks/useWallet';
import { useVestingVault } from '../hooks/useVestingVault';
import { useReferralManager } from '../hooks/useReferralManager';
import { useUSDTToken } from '../hooks/useUSDTToken';
import { useLPToken } from '../hooks/useLPToken';
import { useUserPackages } from '../hooks/useUserPackages';
import { useShareToken } from '../hooks/useShareToken';

interface DashboardPageProps {
  onNavigate: (page: string) => void;
}

const portfolioData = [
  { name: 'Jan', value: 1000 },
  { name: 'Feb', value: 1200 },
  { name: 'Mar', value: 1800 },
  { name: 'Apr', value: 2200 },
  { name: 'May', value: 2800 },
  { name: 'Jun', value: 3200 },
];

// This will be calculated dynamically inside the component

export const DashboardPage: React.FC<DashboardPageProps> = ({ onNavigate }) => {
  const { user, isConnected } = useWallet();
  const {
    vestedAmount,
    claimableAmount,
    claimVestedTokens,
    isPending: isClaimPending,
    isSuccess: isClaimSuccess,
    refetchClaimableAmount,
    refetchVestedAmount
  } = useVestingVault();

 
  const { totalEarnings: referralEarnings, referralCount } = useReferralManager();
  const { balance: usdtBalance } = useUSDTToken();
  const { balance: lpBalance } = useLPToken();
  const { userPackages, totalInvested } = useUserPackages();
  const { balance: shareBalance } = useShareToken();

  // Calculate  allocation based on on-chain data
  const totalValue = parseFloat(shareBalance) + parseFloat(vestedAmount) + parseFloat(lpBalance);
  const sharePercentage = totalValue > 0 ? (parseFloat(shareBalance) / totalValue) * 100 : 0;
  const vestingPercentage = totalValue > 0 ? (parseFloat(vestedAmount) / totalValue) * 100 : 0;
  const lpPercentage = totalValue > 0 ? (parseFloat(lpBalance) / totalValue) * 100 : 0;

  const allocationData = [
    { name: 'Share Tokens', value: Math.round(sharePercentage), color: '#4F46E5' },
    { name: 'Vesting', value: Math.round(vestingPercentage), color: '#10B981' },
    { name: 'LP Tokens', value: Math.round(lpPercentage), color: '#F59E0B' },
  ].filter(item => item.value > 0); // Only show non-zero allocations

  useEffect(() => {
    if (isClaimSuccess) {
      // Refresh data after successful claim
      refetchClaimableAmount();
      refetchVestedAmount();
    }
  }, [isClaimSuccess, refetchClaimableAmount, refetchVestedAmount]);

  const handleClaim = async () => {
    try {
      await claimVestedTokens();
    } catch (error) {
      console.error('Failed to claim tokens:', error);
    }
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-dark-50 flex items-center justify-center">
        <Card className="text-center max-w-md">
          <Wallet className="w-12 h-12 text-dark-400 mx-auto mb-4" />
          <h2 className="text-xl font-heading font-semibold text-dark-900 mb-2">
            Connect Your Wallet
          </h2>
          <p className="text-dark-600 mb-6">
            Please connect your wallet to view your dashboard and portfolio information.
          </p>
          <Button onClick={() => onNavigate('home')}>
            Go to Home Page
          </Button>
        </Card>
      </div>
    );
  }

  const stats = [
    {
      title: 'USDT Balance',
      value: `$${parseFloat(usdtBalance).toLocaleString()}`,
      change: 'Available for investment',
      icon: DollarSign,
      color: 'primary',
    },
    {
      title: 'Vested Tokens',
      value: `${parseFloat(vestedAmount).toLocaleString()} Tokens`,
      change: `${parseFloat(claimableAmount).toLocaleString()} claimable`,
      icon: Clock,
      color: 'success',
    },
    {
      title: 'Share Tokens',
      value: `${parseFloat(shareBalance).toLocaleString()} Tokens`,
      change: `$${totalInvested.toLocaleString()} invested`,
      icon: TrendingUp,
      color: 'primary',
    },
    {
      title: 'Referral Earnings',
      value: `${parseFloat(referralEarnings).toLocaleString()} Tokens`,
      change: `${referralCount} referrals`,
      icon: Users,
      color: 'success',
    },
  ];

  // Generate recent transactions from user packages and other activities
  const recentTransactions = [
    ...userPackages.slice(0, 2).map((pkg) => ({
      id: `pkg-${pkg.packageId}`,
      type: 'Package Purchase',
      amount: parseFloat(pkg.entryAmount),
      date: pkg.purchaseDate || new Date().toISOString().split('T')[0],
      status: 'completed' as const,
    })),
    {
      id: 'claim-1',
      type: 'Token Claim',
      amount: parseFloat(vestedAmount) - parseFloat(claimableAmount),
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'completed' as const,
    },
    {
      id: 'referral-1',
      type: 'Referral Reward',
      amount: parseFloat(referralEarnings),
      date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'completed' as const,
    },
  ].filter(tx => tx.amount > 0).slice(0, 3); // Show only non-zero transactions, max 3

  return (
    <div className="min-h-screen bg-dark-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-heading font-bold text-dark-900 mb-2">
                Welcome back!
              </h1>
              <p className="text-dark-600">
                Address: {user!.address}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button onClick={() => onNavigate('purchase')}>
                <ArrowUpRight className="w-4 h-4 mr-2" />
                Invest More
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card hover>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-dark-600 text-sm mb-1">{stat.title}</p>
                    <h3 className="text-2xl font-heading font-bold text-dark-900">
                      {stat.value}
                    </h3>
                    <div className={`text-sm font-medium ${
                      stat.color === 'primary' ? 'text-primary-600' : 'text-success-600'
                    }`}>
                      {stat.change}
                    </div>
                  </div>
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    stat.color === 'primary' ? 'bg-primary-100' : 'bg-success-100'
                  }`}>
                    <stat.icon className={`w-6 h-6 ${
                      stat.color === 'primary' ? 'text-primary-600' : 'text-success-600'
                    }`} />
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Portfolio Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="lg:col-span-2"
          >
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-heading font-semibold text-dark-900">
                  Portfolio Performance
                </h3>
                <div className="flex items-center text-success-600">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  <span className="text-sm font-medium">+24.5%</span>
                </div>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={portfolioData}>
                    <XAxis 
                      dataKey="name" 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#6B7280', fontSize: 12 }}
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#6B7280', fontSize: 12 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="#4F46E5" 
                      strokeWidth={3}
                      dot={false}
                      strokeDasharray="0"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </motion.div>

          {/* Allocation Pie Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
                Asset Allocation
              </h3>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={allocationData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      dataKey="value"
                    >
                      {allocationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-2 mt-4">
                {allocationData.map((item) => (
                  <div key={item.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-sm text-dark-600">{item.name}</span>
                    </div>
                    <span className="text-sm font-medium text-dark-900">
                      {item.value}%
                    </span>
                  </div>
                ))}
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Recent Activity & Vesting Schedule */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          {/* Recent Transactions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Card>
              <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
                Recent Activity
              </h3>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 bg-dark-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-dark-900">{transaction.type}</h4>
                      <p className="text-sm text-dark-600">{transaction.date}</p>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-dark-900">
                        ${transaction.amount.toLocaleString()}
                      </div>
                      <div className="text-sm text-success-600 capitalize">
                        {transaction.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </motion.div>

          {/* Vesting Schedule */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Card>
              <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
                Vesting Schedule
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-primary-600" />
                    <div>
                      <h4 className="font-medium text-primary-900">Available to Claim</h4>
                      <p className="text-sm text-primary-700">Claim anytime</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-primary-600">{parseFloat(claimableAmount).toLocaleString()} Tokens</div>
                    <Button 
                      size="sm" 
                      className="mt-2" 
                      onClick={handleClaim}
                      disabled={isClaimPending || parseFloat(claimableAmount) <= 0}
                    >
                      {isClaimPending ? 'Processing...' : 'Claim'}
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-dark-600">Total Vested</span>
                    <span className="font-medium">{parseFloat(vestedAmount).toLocaleString()} Tokens</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-dark-600">Claimed</span>
                    <span className="font-medium">{parseFloat(vestedAmount) - parseFloat(claimableAmount) <= 0 ? '0 Tokens' : `${parseFloat(vestedAmount) - parseFloat(claimableAmount)}.00 Tokens`}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-dark-600">Remaining</span>
                    <span className="font-medium text-primary-600">{parseFloat(claimableAmount) <= 0 ? '0 Tokens' : `${parseFloat(claimableAmount)}.00 Tokens`}</span>
                  </div>
                  <div className="w-full bg-dark-200 rounded-full h-2">
                    <div 
                      className="bg-primary-500 h-2 rounded-full" 
                      style={{ width: `${(parseFloat(vestedAmount) - parseFloat(claimableAmount)) / parseFloat(vestedAmount) * 100 || 0}%` }}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
