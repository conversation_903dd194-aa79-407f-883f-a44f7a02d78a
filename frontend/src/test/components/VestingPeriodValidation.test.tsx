import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { PurchasePage } from '../../pages/PurchasePage';
import { PackageCard } from '../../components/purchase/PackageCard';
import { PaymentForm } from '../../components/purchase/PaymentForm';
import { Package } from '../../types';

// Mock the hooks
vi.mock('../../hooks/useWallet', () => ({
  useWallet: () => ({
    isConnected: true,
    user: { address: '0x123' }
  })
}));

vi.mock('../../hooks/usePackageManager', () => ({
  usePackageManager: () => ({
    packageCount: 3,
    getPackageDetails: vi.fn().mockImplementation((id: number) => Promise.resolve({
      entryUSDT: BigInt('1000000000000000000000'), // 1000 USDT
      tokenPriceBps: 1200, // 12% APY (correct field name from contract)
      vestSplitBps: 5000, // 50% vesting allocation (NOT duration)
      referralRateBps: 500, // 5% referral
      active: true,
      exists: true,
    })),
    purchasePackage: vi.fn(),
    isPending: false,
    isSuccess: false
  })
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
}));

describe('Vesting Period Validation', () => {
  const mockPackageWith5YearVesting: Package = {
    id: '1',
    name: 'Test Package',
    minAmount: 1000,
    maxAmount: 10000,
    vestingPeriod: 60, // 5 years = 60 months
    interestRate: 12,
    description: 'Test package with 5-year vesting',
    features: [
      '5-year vesting schedule',
      'Vesting allocation: 50%',
      'Immediate liquidity: 50%',
    ],
    contractData: {
      vestSplitBps: 5000 // 50% vesting allocation
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('PackageCard Component', () => {
    it('should display 5 years vesting period correctly', () => {
      render(
        <PackageCard
          package={mockPackageWith5YearVesting}
          onSelect={vi.fn()}
          selected={false}
        />
      );

      // Should show "5 years vesting (60 months)"
      expect(screen.getByText(/5 years vesting \(60 months\)/)).toBeInTheDocument();
    });

    it('should not display vesting period in months only', () => {
      render(
        <PackageCard
          package={mockPackageWith5YearVesting}
          onSelect={vi.fn()}
          selected={false}
        />
      );

      // Should NOT show just "60 months vesting"
      expect(screen.queryByText('60 months vesting')).not.toBeInTheDocument();
    });
  });

  describe('PaymentForm Component', () => {
    it('should display correct vesting period in payment summary', () => {
      render(
        <PaymentForm
          selectedPackage={mockPackageWith5YearVesting}
          onPayment={vi.fn()}
        />
      );

      // Enter an amount to trigger the summary
      const amountInput = screen.getByLabelText(/Investment Amount/);
      amountInput.setAttribute('value', '1000');

      // Should show "5 years (60 months)" in the summary
      expect(screen.getByText(/5 years \(60 months\)/)).toBeInTheDocument();
    });
  });

  describe('PurchasePage Integration', () => {
    it('should correctly map contract vestSplitBps to vesting allocation, not duration', async () => {
      render(<PurchasePage onNavigate={vi.fn()} />);

      // Wait for packages to load
      await waitFor(() => {
        // Should show 5-year vesting period for all packages
        const vestingTexts = screen.getAllByText(/5 years vesting/);
        expect(vestingTexts.length).toBeGreaterThan(0);
      });

      // Should not show incorrect short vesting periods
      expect(screen.queryByText(/3 months vesting/)).not.toBeInTheDocument();
      expect(screen.queryByText(/6 months vesting/)).not.toBeInTheDocument();
      expect(screen.queryByText(/12 months vesting/)).not.toBeInTheDocument();
    });

    it('should display vesting allocation information in package features', async () => {
      render(<PurchasePage onNavigate={vi.fn()} />);

      await waitFor(() => {
        // Should show vesting allocation information
        expect(screen.getByText(/Vesting allocation: 50%/)).toBeInTheDocument();
        expect(screen.getByText(/Immediate liquidity: 50%/)).toBeInTheDocument();
      });
    });

    it('should show correct vesting information in selection summary', async () => {
      const mockNavigate = vi.fn();
      render(<PurchasePage onNavigate={mockNavigate} />);

      await waitFor(() => {
        // Find and click a package
        const selectButton = screen.getAllByText(/Select Package/)[0];
        selectButton.click();
      });

      // Should show correct vesting period in the selection summary
      await waitFor(() => {
        expect(screen.getByText(/5 years\) vesting period/)).toBeInTheDocument();
      });
    });
  });

  describe('Contract Data Mapping', () => {
    it('should correctly interpret vestSplitBps as allocation percentage, not duration', () => {
      const contractData = {
        vestSplitBps: 5000, // 50% allocation
        tokenPriceBps: 1200, // 12% APY (correct field name from contract)
        entryUSDT: BigInt('1000000000000000000000')
      };

      // vestSplitBps should be interpreted as percentage allocation
      const vestingAllocation = contractData.vestSplitBps / 100; // 50%
      const immediateAllocation = 100 - vestingAllocation; // 50%

      expect(vestingAllocation).toBe(50);
      expect(immediateAllocation).toBe(50);

      // Vesting duration should always be 60 months (5 years) regardless of vestSplitBps
      const vestingDuration = 60; // months
      expect(vestingDuration).toBe(60);
    });

    it('should handle different vestSplitBps values correctly', () => {
      const testCases = [
        { vestSplitBps: 3000, expectedAllocation: 30, expectedDuration: 60 },
        { vestSplitBps: 7000, expectedAllocation: 70, expectedDuration: 60 },
        { vestSplitBps: 10000, expectedAllocation: 100, expectedDuration: 60 },
      ];

      testCases.forEach(({ vestSplitBps, expectedAllocation, expectedDuration }) => {
        const allocation = vestSplitBps / 100;
        const duration = 60; // Always 5 years

        expect(allocation).toBe(expectedAllocation);
        expect(duration).toBe(expectedDuration);
      });
    });
  });
});
