// Test data that matches PackageManager.sol contract requirements

export const validPackageData = {
  id: '1',
  entryAmount: '1000',
  priceBps: '12', // 12% APY
  vestSplitBps: '50', // 50% vesting
  referralRateBps: '5', // 5% referral rate
}

export const invalidPackageData = {
  emptyId: {
    id: '',
    entryAmount: '1000',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  zeroId: {
    id: '0',
    entryAmount: '1000',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  emptyEntryAmount: {
    id: '1',
    entryAmount: '',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  zeroEntryAmount: {
    id: '1',
    entryAmount: '0',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  invalidPriceBps: {
    id: '1',
    entryAmount: '1000',
    priceBps: '101', // Over 100%
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  zeroPriceBps: {
    id: '1',
    entryAmount: '1000',
    priceBps: '0',
    vestSplitBps: '50',
    referralRateBps: '5',
  },
  invalidVestSplitBps: {
    id: '1',
    entryAmount: '1000',
    priceBps: '12',
    vestSplitBps: '101', // Over 100%
    referralRateBps: '5',
  },
  invalidReferralRateBps: {
    id: '1',
    entryAmount: '1000',
    priceBps: '12',
    vestSplitBps: '50',
    referralRateBps: '101', // Over 100%
  },
}

export const mockPackageFromContract = {
  entryUSDT: BigInt('1000000000000000000000'), // 1000 USDT with 18 decimals
  tokenPriceBps: 1200, // 12% APY in basis points (correct field name from contract)
  vestSplitBps: 5000, // 50% vesting in basis points
  referralRateBps: 500, // 5% referral in basis points
  active: true,
  exists: true,
}

export const mockFormattedPackage = {
  id: 0,
  name: 'Package 1',
  minAmount: 1000,
  apy: 12,
  vesting: 50,
  active: true,
  referralRate: 5,
}

export const mockAdminStats = {
  totalUsers: 1247,
  totalValueLocked: 2547890,
  totalTransactions: 12847,
  activePackages: 3,
  pendingWithdrawals: 24500,
}

// Contract function call expectations
export const expectedAddPackageArgs = [
  BigInt('1'), // Package ID
  BigInt('1000000000000000000000'), // Entry amount in wei (1000 USDT)
  1200, // Price in basis points (12% * 100)
  5000, // Vest split in basis points (50% * 100)
  500, // Referral rate in basis points (5% * 100)
]

export const expectedTogglePackageArgs = {
  enable: [BigInt('0')], // Package ID for enabling
  disable: [BigInt('0')], // Package ID for disabling
}
