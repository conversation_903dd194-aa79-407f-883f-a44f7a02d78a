#!/usr/bin/env node

/**
 * Smart Contract Integration Test Runner
 * 
 * This script runs comprehensive tests for the smart contract integration
 * changes made to the DashboardPage and related hooks.
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

interface TestSuite {
  name: string;
  pattern: string;
  description: string;
}

const testSuites: TestSuite[] = [
  {
    name: 'Hook Unit Tests',
    pattern: 'src/hooks/__tests__/*.test.ts',
    description: 'Tests for all newly created and updated hooks'
  },
  {
    name: 'DashboardPage Component Tests',
    pattern: 'src/pages/__tests__/DashboardPage.test.tsx',
    description: 'Integration tests for DashboardPage component'
  },
  {
    name: 'Cross-Hook Integration Tests',
    pattern: 'src/hooks/__tests__/integration.test.ts',
    description: 'Tests for hook dependencies and contract interactions'
  },
  {
    name: 'Existing Package Manager Tests',
    pattern: 'src/hooks/__tests__/usePackageManager.test.ts',
    description: 'Regression tests for existing functionality'
  },
  {
    name: 'Existing OnChain Packages Tests',
    pattern: 'src/hooks/__tests__/useOnChainPackages.test.ts',
    description: 'Regression tests for existing functionality'
  }
];

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(): void {
  console.log(colorize('\n🧪 Smart Contract Integration Test Suite', 'cyan'));
  console.log(colorize('=' .repeat(50), 'cyan'));
  console.log(colorize('Testing comprehensive smart contract integration changes', 'blue'));
  console.log(colorize('for DashboardPage and related hooks.\n', 'blue'));
}

function printTestSuiteInfo(): void {
  console.log(colorize('📋 Test Suites:', 'yellow'));
  testSuites.forEach((suite, index) => {
    console.log(colorize(`  ${index + 1}. ${suite.name}`, 'bright'));
    console.log(colorize(`     ${suite.description}`, 'reset'));
    console.log(colorize(`     Pattern: ${suite.pattern}`, 'magenta'));
    console.log();
  });
}

function checkTestFiles(): boolean {
  console.log(colorize('🔍 Checking test files...', 'yellow'));
  
  let allFilesExist = true;
  const expectedFiles = [
    'src/hooks/__tests__/useReferralManager.test.ts',
    'src/hooks/__tests__/useUSDTToken.test.ts',
    'src/hooks/__tests__/useLPToken.test.ts',
    'src/hooks/__tests__/useUserPackages.test.ts',
    'src/hooks/__tests__/useVestingVault.test.ts',
    'src/hooks/__tests__/integration.test.ts',
    'src/pages/__tests__/DashboardPage.test.tsx',
  ];

  expectedFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (existsSync(fullPath)) {
      console.log(colorize(`  ✅ ${file}`, 'green'));
    } else {
      console.log(colorize(`  ❌ ${file} - NOT FOUND`, 'red'));
      allFilesExist = false;
    }
  });

  console.log();
  return allFilesExist;
}

function runTestSuite(suite: TestSuite): boolean {
  console.log(colorize(`\n🧪 Running: ${suite.name}`, 'cyan'));
  console.log(colorize(`📝 ${suite.description}`, 'blue'));
  console.log(colorize('-'.repeat(40), 'cyan'));

  try {
    const command = `npm test -- ${suite.pattern} --reporter=verbose`;
    console.log(colorize(`Command: ${command}`, 'magenta'));
    
    execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(colorize(`✅ ${suite.name} - PASSED`, 'green'));
    return true;
  } catch (error) {
    console.log(colorize(`❌ ${suite.name} - FAILED`, 'red'));
    console.error(error);
    return false;
  }
}

function runCoverageReport(): void {
  console.log(colorize('\n📊 Generating Coverage Report...', 'yellow'));
  
  try {
    const coverageCommand = 'npm test -- --coverage --coverage.include="src/hooks/**" --coverage.include="src/pages/DashboardPage.tsx"';
    execSync(coverageCommand, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(colorize('✅ Coverage report generated', 'green'));
  } catch (error) {
    console.log(colorize('❌ Coverage report failed', 'red'));
    console.error(error);
  }
}

function printSummary(results: boolean[]): void {
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(colorize('\n📈 Test Summary', 'cyan'));
  console.log(colorize('=' .repeat(30), 'cyan'));
  
  if (passed === total) {
    console.log(colorize(`🎉 All tests passed! (${passed}/${total})`, 'green'));
    console.log(colorize('✅ Smart contract integration is working correctly', 'green'));
  } else {
    console.log(colorize(`⚠️  Some tests failed (${passed}/${total})`, 'yellow'));
    console.log(colorize('❌ Please review failed tests before deployment', 'red'));
  }

  console.log(colorize('\n🔍 Test Coverage Areas:', 'blue'));
  console.log(colorize('  • Hook unit tests with mock contract responses', 'reset'));
  console.log(colorize('  • Component integration with real on-chain data', 'reset'));
  console.log(colorize('  • Cross-hook dependencies and data consistency', 'reset'));
  console.log(colorize('  • Error handling and edge cases', 'reset'));
  console.log(colorize('  • TypeScript interfaces and type safety', 'reset'));
  console.log(colorize('  • BigInt handling and decimal precision', 'reset'));
  console.log(colorize('  • Wagmi hook integration and query patterns', 'reset'));
}

function main(): void {
  printHeader();
  printTestSuiteInfo();
  
  // Check if all test files exist
  if (!checkTestFiles()) {
    console.log(colorize('❌ Some test files are missing. Please ensure all tests are created.', 'red'));
    process.exit(1);
  }

  console.log(colorize('🚀 Starting test execution...', 'green'));
  
  const results: boolean[] = [];
  
  // Run each test suite
  for (const suite of testSuites) {
    const result = runTestSuite(suite);
    results.push(result);
    
    // Add a small delay between test suites
    if (suite !== testSuites[testSuites.length - 1]) {
      console.log(colorize('\n⏳ Preparing next test suite...', 'yellow'));
    }
  }

  // Generate coverage report
  runCoverageReport();
  
  // Print final summary
  printSummary(results);
  
  // Exit with appropriate code
  const allPassed = results.every(Boolean);
  process.exit(allPassed ? 0 : 1);
}

// Run the test suite if this file is executed directly
if (require.main === module) {
  main();
}

export { main, testSuites, runTestSuite };
