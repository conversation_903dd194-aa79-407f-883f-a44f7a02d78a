import { useState, useEffect, useCallback } from 'react';
import { useReadContract } from 'wagmi';
import { formatUnits } from 'viem';
import { OnChainPackage } from '../types';
import { usePackageManager } from './usePackageManager';
import { getVestingVaultContract, getPackageManagerContract } from '../contracts';

interface UseOnChainPackagesReturn {
  packages: OnChainPackage[];
  loading: boolean;
  error: string | null;
  vestingPeriodMonths: number;
  refetch: () => void;
}

export const useOnChainPackages = (): UseOnChainPackagesReturn => {
  const [packages, setPackages] = useState<OnChainPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [vestingPeriodMonths, setVestingPeriodMonths] = useState(60); // Default fallback

  const { packageCount, getPackageDetails } = usePackageManager();

  // Get package IDs directly from the hook
  const { data: packageIds } = useReadContract({
    ...getPackageManagerContract(),
    functionName: 'getPackageIds',
  });

  // Get vesting configuration from VestingVault
  const { data: defaultCliff } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'defaultCliff',
  });

  const { data: defaultDuration } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'defaultDuration',
  });

  // Calculate vesting period in months from contract data
  useEffect(() => {
    if (defaultCliff && defaultDuration) {
      // Convert seconds to months (approximate)
      const cliffMonths = Number(defaultCliff) / (30 * 24 * 60 * 60); // 30 days per month
      const durationMonths = Number(defaultDuration) / (30 * 24 * 60 * 60);
      const totalVestingMonths = Math.round(cliffMonths + durationMonths);
      setVestingPeriodMonths(totalVestingMonths);
    }
  }, [defaultCliff, defaultDuration]);

  const fetchPackages = useCallback(async () => {
    if (!packageIds || !Array.isArray(packageIds) || packageIds.length === 0) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const pkgs: OnChainPackage[] = [];
      const packageIdArray = packageIds as bigint[];

      for (let i = 0; i < packageIdArray.length; i++) {
        const packageId = Number(packageIdArray[i]);
        try {
          const pkg = await getPackageDetails(packageId);

          if (pkg.exists) {
            // Validate package data before processing
            if (!pkg.entryUSDT || typeof pkg.entryUSDT !== 'bigint') {
              console.error(`Invalid entryUSDT for package ${packageId}:`, pkg.entryUSDT);
              continue;
            }
            if (!pkg.tokenPriceBps || typeof pkg.tokenPriceBps !== 'number' || pkg.tokenPriceBps <= 0) {
              console.error(`Invalid tokenPriceBps for package ${packageId}:`, pkg.tokenPriceBps);
              continue;
            }
            if (typeof pkg.vestSplitBps !== 'number') {
              console.error(`Invalid vestSplitBps for package ${packageId}:`, pkg.vestSplitBps);
              continue;
            }
            if (typeof pkg.referralRateBps !== 'number') {
              console.error(`Invalid referralRateBps for package ${packageId}:`, pkg.referralRateBps);
              continue;
            }

            // Calculate tokens minted using exact on-chain logic with safe BigInt conversion
            let tokensMinted: bigint;
            try {
              tokensMinted = (pkg.entryUSDT * BigInt(1e18) * BigInt(100)) / BigInt(pkg.tokenPriceBps);
            } catch (error) {
              console.error(`Error calculating tokens minted for package ${packageId}:`, error);
              continue;
            }

            const onChainPackage: OnChainPackage = {
              id: packageId.toString(),
              name: `Package ${packageId}`,
              minAmount: Number(formatUnits(pkg.entryUSDT, 6)), // USDT has 6 decimals
              maxAmount: Number(formatUnits(pkg.entryUSDT, 6)) * 10, // Example max amount
              vestingPeriod: vestingPeriodMonths,
              interestRate: pkg.tokenPriceBps / 100, // Convert basis points to percentage
              description: `Investment package with ${pkg.tokenPriceBps / 100}% returns and ${pkg.vestSplitBps / 100}% vesting allocation`,
              features: [
                `Entry amount: ${formatUnits(pkg.entryUSDT, 6)} USDT`,
                `Vesting allocation: ${pkg.vestSplitBps / 100}%`,
                `Immediate liquidity: ${100 - (pkg.vestSplitBps / 100)}%`,
                `Referral rate: ${pkg.referralRateBps / 100}%`,
                `${Math.floor(vestingPeriodMonths / 12)} year${Math.floor(vestingPeriodMonths / 12) !== 1 ? 's' : ''} vesting schedule`,
                `Tokens minted: ${formatUnits(tokensMinted, 18)} SHARE`,
              ],
              contractData: pkg,
              // OnChainPackage specific fields
              entryUSDT: pkg.entryUSDT,
              priceBps: pkg.tokenPriceBps, // Map tokenPriceBps to priceBps for compatibility
              vestSplitBps: pkg.vestSplitBps,
              referralRateBps: pkg.referralRateBps,
              active: pkg.exists, // Using exists as active status
            };

            pkgs.push(onChainPackage);
          }
        } catch (packageError) {
          console.error(`Error fetching package ${packageId}:`, packageError);
          // Continue with other packages even if one fails
        }
      }

      setPackages(pkgs);
    } catch (err) {
      console.error('Error fetching packages:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch packages');
    } finally {
      setLoading(false);
    }
  }, [packageIds, getPackageDetails, vestingPeriodMonths]);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  const refetch = () => {
    fetchPackages();
  };

  return {
    packages,
    loading,
    error,
    vestingPeriodMonths,
    refetch,
  };
};
