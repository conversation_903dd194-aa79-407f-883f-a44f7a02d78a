import { useAccount, useReadContract } from 'wagmi';
import { getUSDTContract, getPackageManagerContract } from '../contracts';
import { formatUnits } from 'viem';

export const useUSDTToken = () => {
  const { address } = useAccount();
  
  // Get USDT balance (6 decimals)
  const { data: usdtBalance, refetch: refetchUSDTBalance } = useReadContract({
    ...getUSDTContract(),
    functionName: 'balanceOf',
    args: [address],
    query: {
      enabled: !!address,
    },
  });

  // Get USDT allowance for PackageManager
  const { data: usdtAllowance, refetch: refetchAllowance } = useReadContract({
    ...getUSDTContract(),
    functionName: 'allowance',
    args: [address, getPackageManagerContract().address],
    query: {
      enabled: !!address,
    },
  });

  // Get USDT total supply
  const { data: usdtTotalSupply } = useReadContract({
    ...getUSDTContract(),
    functionName: 'totalSupply',
  });

  // Get USDT decimals
  const { data: usdtDecimals } = useReadContract({
    ...getUSDTContract(),
    functionName: 'decimals',
  });

  const decimals = usdtDecimals || 6;
  
  return {
    balance: usdtBalance ? formatUnits(usdtBalance, decimals) : '0',
    allowance: usdtAllowance ? formatUnits(usdtAllowance, decimals) : '0',
    totalSupply: usdtTotalSupply ? formatUnits(usdtTotalSupply, decimals) : '0',
    decimals,
    refetchBalance: refetchUSDTBalance,
    refetchAllowance,
  };
};
