import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useVestingVault } from '../useVestingVault';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
  useWriteContract: vi.fn(),
}));

// Mock viem utilities
vi.mock('viem', () => ({
  formatEther: vi.fn((value: bigint) => {
    if (typeof value !== 'bigint') return '0'
    const divisor = BigInt(10 ** 18)
    const result = Number(value) / Number(divisor)
    return result.toString()
  }),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getVestingVaultContract: vi.fn(() => ({
    address: '0xVestingVault',
    abi: [],
  })),
}));

import { useAccount, useReadContract, useWriteContract } from 'wagmi';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;
const mockUseWriteContract = useWriteContract as any;

describe('useVestingVault', () => {
  const mockAddress = '******************************************';
  const mockWriteContractAsync = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });

    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: false,
      isSuccess: false,
      error: null,
    });
  });

  it('should return default values when no data is available', () => {
    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('0');
    expect(result.current.claimableAmount).toBe('0');
    expect(result.current.isPending).toBe(false);
    expect(result.current.isSuccess).toBe(false);
    expect(result.current.error).toBe(null);
    expect(typeof result.current.claimVestedTokens).toBe('function');
    expect(typeof result.current.refetchVestedAmount).toBe('function');
    expect(typeof result.current.refetchClaimableAmount).toBe('function');
  });

  it('should format vested and claimable amounts correctly', () => {
    const mockVestedAmount = BigInt('5000000000000000000000'); // 5000 tokens (18 decimals)
    const mockClaimableAmount = BigInt('1500000000000000000000'); // 1500 tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockVestedAmount,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockClaimableAmount,
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('5000');
    expect(result.current.claimableAmount).toBe('1500');
  });

  it('should handle small token amounts correctly', () => {
    const mockVestedAmount = BigInt('1000000000000000000'); // 1 token
    const mockClaimableAmount = BigInt('500000000000000000'); // 0.5 tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockVestedAmount,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockClaimableAmount,
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('1');
    expect(result.current.claimableAmount).toBe('0.5');
  });

  it('should handle zero amounts', () => {
    const mockVestedAmount = BigInt('0');
    const mockClaimableAmount = BigInt('0');

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockVestedAmount,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockClaimableAmount,
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('0');
    expect(result.current.claimableAmount).toBe('0');
  });

  it('should handle non-bigint data gracefully', () => {
    mockUseReadContract
      .mockReturnValueOnce({
        data: 'invalid-data', // Not a BigInt
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: null, // Null data
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('0');
    expect(result.current.claimableAmount).toBe('0');
  });

  it('should handle undefined data', () => {
    mockUseReadContract
      .mockReturnValueOnce({
        data: undefined,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: undefined,
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('0');
    expect(result.current.claimableAmount).toBe('0');
  });

  it('should handle no connected address', () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('0');
    expect(result.current.claimableAmount).toBe('0');
  });

  it('should provide refetch functions', () => {
    const mockRefetchVested = vi.fn();
    const mockRefetchClaimable = vi.fn();

    mockUseReadContract
      .mockReturnValueOnce({
        data: BigInt('1000000000000000000000'),
        refetch: mockRefetchVested,
      })
      .mockReturnValueOnce({
        data: BigInt('500000000000000000000'),
        refetch: mockRefetchClaimable,
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.refetchVestedAmount).toBe(mockRefetchVested);
    expect(result.current.refetchClaimableAmount).toBe(mockRefetchClaimable);
  });

  it('should call claimVestedTokens correctly', async () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
      refetch: vi.fn(),
    });

    mockWriteContractAsync.mockResolvedValue({ hash: '0xmockhash' });

    const { result } = renderHook(() => useVestingVault());

    await result.current.claimVestedTokens();

    expect(mockWriteContractAsync).toHaveBeenCalledWith({
      address: '0xVestingVault',
      abi: [],
      functionName: 'claim',
    });
  });

  it('should handle claim transaction errors', async () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
      refetch: vi.fn(),
    });

    const mockError = new Error('Transaction failed');
    mockWriteContractAsync.mockRejectedValue(mockError);

    const { result } = renderHook(() => useVestingVault());

    await expect(result.current.claimVestedTokens()).rejects.toThrow('Transaction failed');
  });

  it('should return write contract states correctly', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
      refetch: vi.fn(),
    });

    mockUseWriteContract.mockReturnValue({
      writeContractAsync: mockWriteContractAsync,
      isPending: true,
      isSuccess: true,
      error: new Error('Mock error'),
    });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.isPending).toBe(true);
    expect(result.current.isSuccess).toBe(true);
    expect(result.current.error).toEqual(new Error('Mock error'));
  });

  it('should use correct contract calls with query enabled', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
      refetch: vi.fn(),
    });

    renderHook(() => useVestingVault());

    // Should call useReadContract twice for vested and claimable amounts
    expect(mockUseReadContract).toHaveBeenCalledTimes(2);
    
    // Check vested amount call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(1, {
      address: '0xVestingVault',
      abi: [],
      functionName: 'getVestedAmount',
      args: [mockAddress],
      query: {
        enabled: true,
      },
    });

    // Check claimable amount call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(2, {
      address: '0xVestingVault',
      abi: [],
      functionName: 'getClaimableAmount',
      args: [mockAddress],
      query: {
        enabled: true,
      },
    });
  });

  it('should handle large token amounts correctly', () => {
    const mockVestedAmount = BigInt('1000000000000000000000000'); // 1M tokens
    const mockClaimableAmount = BigInt('250000000000000000000000'); // 250K tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockVestedAmount,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockClaimableAmount,
        refetch: vi.fn(),
      });

    const { result } = renderHook(() => useVestingVault());

    expect(result.current.vestedAmount).toBe('1000000');
    expect(result.current.claimableAmount).toBe('250000');
  });
});
