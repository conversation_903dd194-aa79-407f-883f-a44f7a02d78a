import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useReferralManager } from '../useReferralManager';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
}));

// Mock viem utilities
vi.mock('viem', () => ({
  formatEther: vi.fn((value: bigint) => {
    if (typeof value !== 'bigint') return '0'
    const divisor = BigInt(10 ** 18)
    const result = Number(value) / Number(divisor)
    return result.toString()
  }),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getShareTokenContract: vi.fn(() => ({
    address: '0xShareToken',
    abi: [],
  })),
  getReferralManagerContract: vi.fn(() => ({
    address: '0xReferralManager',
    abi: [],
  })),
}));

import { useAccount, useReadContract } from 'wagmi';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;

describe('useReferralManager', () => {
  const mockAddress = '******************************************';

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });
  });

  it('should return default values when no data is available', () => {
    mockUseReadContract.mockReturnValue({
      data: undefined,
    });

    const { result } = renderHook(() => useReferralManager());

    expect(result.current.totalEarnings).toBe('0.00');
    expect(result.current.referralCount).toBe(0);
    expect(result.current.availableBalance).toBe('0');
  });

  it('should calculate referral data from user share balance', () => {
    const mockUserShareBalance = BigInt('1000000000000000000000'); // 1000 tokens
    const mockReferralManagerBalance = BigInt('5000000000000000000000'); // 5000 tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockReferralManagerBalance, // First call for referral manager balance
      })
      .mockReturnValueOnce({
        data: mockUserShareBalance, // Second call for user share balance
      });

    const { result } = renderHook(() => useReferralManager());

    // Should estimate 10% of share balance as referral earnings (100 tokens)
    expect(result.current.totalEarnings).toBe('100.00');
    // Should estimate referral count based on 150 tokens per referral
    expect(result.current.referralCount).toBe(0); // 100/150 = 0 (floor)
    expect(result.current.availableBalance).toBe('5000');
  });

  it('should handle large share balances correctly', () => {
    const mockUserShareBalance = BigInt('15000000000000000000000'); // 15000 tokens
    const mockReferralManagerBalance = BigInt('10000000000000000000000'); // 10000 tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockReferralManagerBalance,
      })
      .mockReturnValueOnce({
        data: mockUserShareBalance,
      });

    const { result } = renderHook(() => useReferralManager());

    // 10% of 15000 = 1500 tokens
    expect(result.current.totalEarnings).toBe('1500.00');
    // 1500/150 = 10 referrals
    expect(result.current.referralCount).toBe(10);
    expect(result.current.availableBalance).toBe('10000');
  });

  it('should handle zero balances', () => {
    const mockUserShareBalance = BigInt('0');
    const mockReferralManagerBalance = BigInt('0');

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockReferralManagerBalance,
      })
      .mockReturnValueOnce({
        data: mockUserShareBalance,
      });

    const { result } = renderHook(() => useReferralManager());

    expect(result.current.totalEarnings).toBe('0.00');
    expect(result.current.referralCount).toBe(0);
    expect(result.current.availableBalance).toBe('0');
  });

  it('should handle undefined data gracefully', () => {
    mockUseReadContract
      .mockReturnValueOnce({
        data: undefined,
      })
      .mockReturnValueOnce({
        data: undefined,
      });

    const { result } = renderHook(() => useReferralManager());

    expect(result.current.totalEarnings).toBe('0.00');
    expect(result.current.referralCount).toBe(0);
    expect(result.current.availableBalance).toBe('0');
  });

  it('should handle no connected address', () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    mockUseReadContract.mockReturnValue({
      data: undefined,
    });

    const { result } = renderHook(() => useReferralManager());

    expect(result.current.totalEarnings).toBe('0.00');
    expect(result.current.referralCount).toBe(0);
    expect(result.current.availableBalance).toBe('0');
  });

  it('should provide refetch function', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
    });

    const { result } = renderHook(() => useReferralManager());

    expect(typeof result.current.refetch).toBe('function');
    
    // Should not throw when called
    expect(() => result.current.refetch()).not.toThrow();
  });

  it('should use correct contract addresses for queries', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
    });

    renderHook(() => useReferralManager());

    // Should call useReadContract twice - once for each balance query
    expect(mockUseReadContract).toHaveBeenCalledTimes(2);
    
    // First call should be for referral manager balance
    expect(mockUseReadContract).toHaveBeenNthCalledWith(1, {
      address: '0xShareToken',
      abi: [],
      functionName: 'balanceOf',
      args: ['0xReferralManager'],
      query: {
        enabled: true,
      },
    });

    // Second call should be for user share balance
    expect(mockUseReadContract).toHaveBeenNthCalledWith(2, {
      address: '0xShareToken',
      abi: [],
      functionName: 'balanceOf',
      args: [mockAddress],
      query: {
        enabled: true,
      },
    });
  });
});
