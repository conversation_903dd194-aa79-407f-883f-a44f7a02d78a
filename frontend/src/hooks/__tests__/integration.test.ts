import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Import all hooks for integration testing
import { useVestingVault } from '../useVestingVault';
import { useReferralManager } from '../useReferralManager';
import { useUSDTToken } from '../useUSDTToken';
import { useLPToken } from '../useLPToken';
import { useUserPackages } from '../useUserPackages';
import { useShareToken } from '../useShareToken';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
  useWriteContract: vi.fn(),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getVestingVaultContract: vi.fn(() => ({ address: '0xVestingVault', abi: [] })),
  getShareTokenContract: vi.fn(() => ({ address: '0xShareToken', abi: [] })),
  getReferralManagerContract: vi.fn(() => ({ address: '0xReferralManager', abi: [] })),
  getUSDTContract: vi.fn(() => ({ address: '0xUSDTToken', abi: [] })),
  getLPTokenContract: vi.fn(() => ({ address: '0xLPToken', abi: [] })),
  getPackageManagerContract: vi.fn(() => ({ address: '0xPackageManager', abi: [] })),
}));

// Mock usePackageManager
vi.mock('../usePackageManager', () => ({
  usePackageManager: vi.fn(),
}));

import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { usePackageManager } from '../usePackageManager';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;
const mockUseWriteContract = useWriteContract as any;
const mockUsePackageManager = usePackageManager as any;

describe('Smart Contract Integration Tests', () => {
  const mockAddress = '0x1234567890123456789012345678901234567890';

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });

    mockUseWriteContract.mockReturnValue({
      writeContractAsync: vi.fn().mockResolvedValue({ hash: '0xmockhash' }),
      isPending: false,
      isSuccess: false,
      error: null,
    });
  });

  describe('Cross-Hook Data Consistency', () => {
    it('should maintain consistent token balance data across hooks', () => {
      const shareTokenBalance = BigInt('10000000000000000000000'); // 10,000 tokens

      // Mock share token balance for both hooks
      mockUseReadContract
        .mockReturnValueOnce({ data: shareTokenBalance, refetch: vi.fn() }) // useShareToken
        .mockReturnValueOnce({ data: BigInt('5000000000000000000000'), refetch: vi.fn() }) // useReferralManager - referral manager balance
        .mockReturnValueOnce({ data: shareTokenBalance, refetch: vi.fn() }); // useReferralManager - user balance

      const shareTokenResult = renderHook(() => useShareToken());
      const referralResult = renderHook(() => useReferralManager());

      expect(shareTokenResult.result.current.balance).toBe('10000.0');
      // Referral manager should use the same share token balance for calculations
      expect(referralResult.result.current.totalEarnings).toBe('1000.00'); // 10% of 10,000
    });

    it('should calculate asset allocation correctly using multiple token balances', () => {
      const shareBalance = BigInt('5000000000000000000000'); // 5,000 tokens
      const vestedAmount = BigInt('3000000000000000000000'); // 3,000 tokens
      const lpBalance = BigInt('2000000000000000000000'); // 2,000 tokens

      // Mock all token balances
      mockUseReadContract
        .mockReturnValueOnce({ data: shareBalance, refetch: vi.fn() }) // useShareToken
        .mockReturnValueOnce({ data: vestedAmount, refetch: vi.fn() }) // useVestingVault - vested
        .mockReturnValueOnce({ data: BigInt('1000000000000000000000'), refetch: vi.fn() }) // useVestingVault - claimable
        .mockReturnValueOnce({ data: lpBalance, refetch: vi.fn() }); // useLPToken

      const shareResult = renderHook(() => useShareToken());
      const vestingResult = renderHook(() => useVestingVault());
      const lpResult = renderHook(() => useLPToken());

      const totalValue = 5000 + 3000 + 2000; // 10,000 total
      const expectedSharePercentage = (5000 / totalValue) * 100; // 50%
      const expectedVestingPercentage = (3000 / totalValue) * 100; // 30%
      const expectedLPPercentage = (2000 / totalValue) * 100; // 20%

      expect(shareResult.result.current.balance).toBe('5000.0');
      expect(vestingResult.result.current.vestedAmount).toBe('3000.0');
      expect(lpResult.result.current.balance).toBe('2000.0');

      // These percentages would be calculated in the DashboardPage component
      expect(Math.round(expectedSharePercentage)).toBe(50);
      expect(Math.round(expectedVestingPercentage)).toBe(30);
      expect(Math.round(expectedLPPercentage)).toBe(20);
    });
  });

  describe('USDT Precision Handling', () => {
    it('should handle USDT 6-decimal precision correctly across all hooks', () => {
      const usdtBalance = BigInt('1500000000'); // 1,500 USDT (6 decimals)
      const usdtAllowance = BigInt('1000000000'); // 1,000 USDT allowance

      mockUseReadContract
        .mockReturnValueOnce({ data: usdtBalance, refetch: vi.fn() }) // USDT balance
        .mockReturnValueOnce({ data: usdtAllowance, refetch: vi.fn() }) // USDT allowance
        .mockReturnValueOnce({ data: BigInt('100000000000000'), refetch: vi.fn() }) // Total supply
        .mockReturnValueOnce({ data: 6, refetch: vi.fn() }); // Decimals

      const { result } = renderHook(() => useUSDTToken());

      expect(result.current.balance).toBe('1500');
      expect(result.current.allowance).toBe('1000');
      expect(result.current.decimals).toBe(6);
    });

    it('should handle package entry amounts with USDT precision', async () => {
      const packageEntryUSDT = BigInt('5000000000'); // 5,000 USDT (6 decimals)

      mockUsePackageManager.mockReturnValue({
        packageCount: 1,
        getPackageDetails: vi.fn().mockResolvedValue({
          entryUSDT: packageEntryUSDT,
          priceBps: 1200,
          vestSplitBps: 5000,
          referralRateBps: 500,
          exists: true,
        }),
      });

      const { result } = renderHook(() => useUserPackages());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.userPackages[0].entryAmount).toBe('5000');
      expect(result.current.totalInvested).toBe(5000);
    });
  });

  describe('Vesting Vault Integration', () => {
    it('should handle vesting claim flow with proper data refresh', async () => {
      const initialClaimable = BigInt('1500000000000000000000'); // 1,500 tokens
      const afterClaimClaimable = BigInt('0'); // 0 tokens after claim
      const mockRefetchClaimable = vi.fn();
      const mockRefetchVested = vi.fn();
      const mockClaimFunction = vi.fn().mockResolvedValue({ hash: '0xclaimhash' });

      mockUseReadContract
        .mockReturnValueOnce({ data: BigInt('5000000000000000000000'), refetch: mockRefetchVested })
        .mockReturnValueOnce({ data: initialClaimable, refetch: mockRefetchClaimable });

      mockUseWriteContract.mockReturnValue({
        writeContractAsync: mockClaimFunction,
        isPending: false,
        isSuccess: false,
        error: null,
      });

      const { result } = renderHook(() => useVestingVault());

      expect(result.current.claimableAmount).toBe('1500.0');

      // Simulate claim
      await result.current.claimVestedTokens();

      expect(mockClaimFunction).toHaveBeenCalledWith({
        address: '0xVestingVault',
        abi: [],
        functionName: 'claim',
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors gracefully across all hooks', () => {
      mockUseReadContract.mockReturnValue({
        data: undefined,
        error: new Error('Network error'),
        refetch: vi.fn(),
      });

      const shareResult = renderHook(() => useShareToken());
      const usdtResult = renderHook(() => useUSDTToken());
      const lpResult = renderHook(() => useLPToken());
      const vestingResult = renderHook(() => useVestingVault());
      const referralResult = renderHook(() => useReferralManager());

      // All hooks should return default values when network fails
      expect(shareResult.result.current.balance).toBe('0');
      expect(usdtResult.result.current.balance).toBe('0');
      expect(lpResult.result.current.balance).toBe('0');
      expect(vestingResult.result.current.vestedAmount).toBe('0');
      expect(referralResult.result.current.totalEarnings).toBe('0.00');
    });

    it('should handle partial data loading states', () => {
      // Mock some hooks with data, others without
      mockUseReadContract
        .mockReturnValueOnce({ data: BigInt('1000000000000000000000'), refetch: vi.fn() }) // Share token loaded
        .mockReturnValueOnce({ data: undefined, refetch: vi.fn() }) // USDT not loaded
        .mockReturnValueOnce({ data: undefined, refetch: vi.fn() }) // USDT allowance not loaded
        .mockReturnValueOnce({ data: undefined, refetch: vi.fn() }) // USDT total supply not loaded
        .mockReturnValueOnce({ data: 6, refetch: vi.fn() }); // USDT decimals loaded

      const shareResult = renderHook(() => useShareToken());
      const usdtResult = renderHook(() => useUSDTToken());

      expect(shareResult.result.current.balance).toBe('1000.0');
      expect(usdtResult.result.current.balance).toBe('0'); // Should handle undefined gracefully
      expect(usdtResult.result.current.decimals).toBe(6); // Should use loaded decimals
    });
  });

  describe('Real-time Data Synchronization', () => {
    it('should provide refetch functions for data synchronization', () => {
      const mockRefetch = vi.fn();
      
      mockUseReadContract.mockReturnValue({
        data: BigInt('1000000000000000000000'),
        refetch: mockRefetch,
      });

      const shareResult = renderHook(() => useShareToken());
      const usdtResult = renderHook(() => useUSDTToken());
      const lpResult = renderHook(() => useLPToken());
      const vestingResult = renderHook(() => useVestingVault());

      // All hooks should provide refetch capabilities
      expect(typeof shareResult.result.current.refetchBalance).toBe('function');
      expect(typeof usdtResult.result.current.refetchBalance).toBe('function');
      expect(typeof lpResult.result.current.refetchBalance).toBe('function');
      expect(typeof vestingResult.result.current.refetchVestedAmount).toBe('function');
      expect(typeof vestingResult.result.current.refetchClaimableAmount).toBe('function');
    });
  });

  describe('Contract Address Validation', () => {
    it('should use correct contract addresses for all hooks', () => {
      mockUseReadContract.mockReturnValue({
        data: BigInt('1000000000000000000000'),
        refetch: vi.fn(),
      });

      renderHook(() => useShareToken());
      renderHook(() => useUSDTToken());
      renderHook(() => useLPToken());
      renderHook(() => useVestingVault());
      renderHook(() => useReferralManager());

      // Verify correct contract addresses are used
      const calls = mockUseReadContract.mock.calls;
      
      // Check that different contract addresses are used
      const contractAddresses = calls.map(call => call[0].address);
      expect(contractAddresses).toContain('0xShareToken');
      expect(contractAddresses).toContain('0xUSDTToken');
      expect(contractAddresses).toContain('0xLPToken');
      expect(contractAddresses).toContain('0xVestingVault');
      expect(contractAddresses).toContain('0xReferralManager');
    });
  });
});
