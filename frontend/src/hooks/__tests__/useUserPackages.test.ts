import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useUserPackages } from '../useUserPackages';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
}));

// Mock viem utilities
vi.mock('viem', () => ({
  formatUnits: vi.fn((value: bigint, decimals: number) => {
    if (typeof value !== 'bigint') return '0'
    const divisor = BigInt(10 ** decimals)
    const result = Number(value) / Number(divisor)
    return result.toString()
  }),
}));

// Mock usePackageManager hook
vi.mock('../usePackageManager', () => ({
  usePackageManager: vi.fn(),
}));

import { useAccount } from 'wagmi';
import { usePackageManager } from '../usePackageManager';

const mockUseAccount = useAccount as any;
const mockUsePackageManager = usePackageManager as any;

describe('useUserPackages', () => {
  const mockAddress = '0x1234567890123456789012345678901234567890';
  const mockGetPackageDetails = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });

    mockUsePackageManager.mockReturnValue({
      packageCount: 3,
      getPackageDetails: mockGetPackageDetails,
    });
  });

  it('should return default values when no address is connected', () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    const { result } = renderHook(() => useUserPackages());

    expect(result.current.userPackages).toEqual([]);
    expect(result.current.totalInvested).toBe(0);
    expect(result.current.averageAPY).toBe(0);
    expect(result.current.packageCount).toBe(0);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should return default values when no packages exist', () => {
    mockUsePackageManager.mockReturnValue({
      packageCount: 0,
      getPackageDetails: mockGetPackageDetails,
    });

    const { result } = renderHook(() => useUserPackages());

    expect(result.current.userPackages).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  it('should fetch and format user packages correctly', async () => {
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('**********'), // 1000 USDT (6 decimals)
        priceBps: 1200, // 12% APY
        vestSplitBps: 5000, // 50% vesting
        referralRateBps: 500, // 5% referral
        exists: true,
      })
      .mockResolvedValueOnce({
        entryUSDT: BigInt('5000000000'), // 5000 USDT
        priceBps: 1500, // 15% APY
        vestSplitBps: 6000, // 60% vesting
        referralRateBps: 300, // 3% referral
        exists: true,
      });

    const { result } = renderHook(() => useUserPackages());

    // Initially loading
    expect(result.current.loading).toBe(true);

    // Wait for packages to load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should have 2 packages (demo assumes user purchased first 2)
    expect(result.current.userPackages).toHaveLength(2);
    
    const firstPackage = result.current.userPackages[0];
    expect(firstPackage.packageId).toBe(0);
    expect(firstPackage.name).toBe('Investment Package 1');
    expect(firstPackage.entryAmount).toBe('1000');
    expect(firstPackage.apy).toBe(12);
    expect(firstPackage.vestingPercentage).toBe(50);
    expect(firstPackage.referralRate).toBe(5);
    expect(firstPackage.status).toBe('active');

    const secondPackage = result.current.userPackages[1];
    expect(secondPackage.packageId).toBe(1);
    expect(secondPackage.entryAmount).toBe('5000');
    expect(secondPackage.apy).toBe(15);
  });

  it('should calculate total invested correctly', async () => {
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('**********'), // 1000 USDT
        priceBps: 1200,
        vestSplitBps: 5000,
        referralRateBps: 500,
        exists: true,
      })
      .mockResolvedValueOnce({
        entryUSDT: BigInt('2500000000'), // 2500 USDT
        priceBps: 1500,
        vestSplitBps: 6000,
        referralRateBps: 300,
        exists: true,
      });

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.totalInvested).toBe(3500); // 1000 + 2500
  });

  it('should calculate average APY correctly', async () => {
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('**********'),
        priceBps: 1000, // 10% APY
        vestSplitBps: 5000,
        referralRateBps: 500,
        exists: true,
      })
      .mockResolvedValueOnce({
        entryUSDT: BigInt('2000000000'),
        priceBps: 2000, // 20% APY
        vestSplitBps: 6000,
        referralRateBps: 300,
        exists: true,
      });

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.averageAPY).toBe(15); // (10 + 20) / 2
  });

  it('should handle package fetch errors gracefully', async () => {
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('**********'),
        priceBps: 1200,
        vestSplitBps: 5000,
        referralRateBps: 500,
        exists: true,
      })
      .mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should still have the first package that succeeded
    expect(result.current.userPackages).toHaveLength(1);
    expect(result.current.error).toBe(null); // Individual package errors don't set global error
  });

  it('should handle general fetch error', async () => {
    mockGetPackageDetails.mockRejectedValue(new Error('General fetch error'));

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.userPackages).toEqual([]);
    expect(result.current.error).toBe('General fetch error');
  });

  it('should provide refetch function', async () => {
    mockGetPackageDetails.mockResolvedValue({
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    });

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(typeof result.current.refetch).toBe('function');
    
    // Should trigger loading when called
    result.current.refetch();
    expect(result.current.loading).toBe(true);
  });

  it('should handle zero package count', () => {
    mockUsePackageManager.mockReturnValue({
      packageCount: 0,
      getPackageDetails: mockGetPackageDetails,
    });

    const { result } = renderHook(() => useUserPackages());

    expect(result.current.userPackages).toEqual([]);
    expect(result.current.totalInvested).toBe(0);
    expect(result.current.averageAPY).toBe(0);
    expect(result.current.packageCount).toBe(0);
    expect(result.current.loading).toBe(false);
  });

  it('should include purchase dates for packages', async () => {
    mockGetPackageDetails.mockResolvedValue({
      entryUSDT: BigInt('**********'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    });

    const { result } = renderHook(() => useUserPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const package1 = result.current.userPackages[0];
    expect(package1.purchaseDate).toBeDefined();
    expect(typeof package1.purchaseDate).toBe('string');
    // Should be a valid date format (YYYY-MM-DD)
    expect(package1.purchaseDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
  });

  it('should handle undefined packageCount', () => {
    mockUsePackageManager.mockReturnValue({
      packageCount: undefined,
      getPackageDetails: mockGetPackageDetails,
    });

    const { result } = renderHook(() => useUserPackages());

    expect(result.current.loading).toBe(false);
    expect(result.current.userPackages).toEqual([]);
  });
});
