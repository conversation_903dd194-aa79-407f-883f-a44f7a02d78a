import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useLPToken } from '../useLPToken';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
}));

// Mock viem utilities
vi.mock('viem', () => ({
  formatEther: vi.fn((value: bigint) => {
    if (typeof value !== 'bigint') return '0'
    const divisor = BigInt(10 ** 18)
    const result = Number(value) / Number(divisor)
    return result.toString()
  }),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getLPTokenContract: vi.fn(() => ({
    address: '0xLPToken',
    abi: [],
  })),
}));

import { useAccount, useReadContract } from 'wagmi';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;

describe('useLPToken', () => {
  const mockAddress = '******************************************';

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });
  });

  it('should return default values when no data is available', () => {
    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
    expect(result.current.name).toBe('LP Token');
    expect(result.current.symbol).toBe('LP');
    expect(typeof result.current.refetchBalance).toBe('function');
  });

  it('should format LP token balance correctly', () => {
    const mockBalance = BigInt('1000000000000000000000'); // 1000 LP tokens (18 decimals)
    const mockTotalSupply = BigInt('10000000000000000000000'); // 10000 LP tokens
    const mockName = 'BlockCoop LP Token';
    const mockSymbol = 'BCLP';

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: mockName,
      })
      .mockReturnValueOnce({
        data: mockSymbol,
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('1000');
    expect(result.current.totalSupply).toBe('10000');
    expect(result.current.name).toBe('BlockCoop LP Token');
    expect(result.current.symbol).toBe('BCLP');
  });

  it('should handle small LP token amounts correctly', () => {
    const mockBalance = BigInt('1000000000000000000'); // 1 LP token (18 decimals)
    const mockTotalSupply = BigInt('1000000000000000000000'); // 1000 LP tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: 'LP Token',
      })
      .mockReturnValueOnce({
        data: 'LP',
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('1');
    expect(result.current.totalSupply).toBe('1000');
  });

  it('should handle fractional LP token amounts', () => {
    const mockBalance = BigInt('500000000000000000'); // 0.5 LP tokens
    const mockTotalSupply = BigInt('2500000000000000000000'); // 2500 LP tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: 'LP Token',
      })
      .mockReturnValueOnce({
        data: 'LP',
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('0.5');
    expect(result.current.totalSupply).toBe('2500');
  });

  it('should handle zero balances', () => {
    const mockBalance = BigInt('0');
    const mockTotalSupply = BigInt('0');

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: 'LP Token',
      })
      .mockReturnValueOnce({
        data: 'LP',
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
  });

  it('should handle undefined token metadata with defaults', () => {
    mockUseReadContract
      .mockReturnValueOnce({
        data: BigInt('1000000000000000000000'),
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: BigInt('10000000000000000000000'),
      })
      .mockReturnValueOnce({
        data: undefined, // No name
      })
      .mockReturnValueOnce({
        data: undefined, // No symbol
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.name).toBe('LP Token');
    expect(result.current.symbol).toBe('LP');
    expect(result.current.balance).toBe('1000');
  });

  it('should handle no connected address', () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
    expect(result.current.name).toBe('LP Token');
    expect(result.current.symbol).toBe('LP');
  });

  it('should provide refetch function for balance', () => {
    const mockRefetchBalance = vi.fn();

    mockUseReadContract
      .mockReturnValueOnce({
        data: BigInt('1000000000000000000000'),
        refetch: mockRefetchBalance,
      })
      .mockReturnValueOnce({
        data: BigInt('10000000000000000000000'),
      })
      .mockReturnValueOnce({
        data: 'LP Token',
      })
      .mockReturnValueOnce({
        data: 'LP',
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.refetchBalance).toBe(mockRefetchBalance);
  });

  it('should use correct contract calls', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('1000000000000000000000'),
      refetch: vi.fn(),
    });

    renderHook(() => useLPToken());

    // Should call useReadContract 4 times for balance, totalSupply, name, symbol
    expect(mockUseReadContract).toHaveBeenCalledTimes(4);
    
    // Check balance call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(1, {
      address: '0xLPToken',
      abi: [],
      functionName: 'balanceOf',
      args: [mockAddress],
      query: {
        enabled: true,
      },
    });

    // Check totalSupply call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(2, {
      address: '0xLPToken',
      abi: [],
      functionName: 'totalSupply',
    });

    // Check name call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(3, {
      address: '0xLPToken',
      abi: [],
      functionName: 'name',
    });

    // Check symbol call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(4, {
      address: '0xLPToken',
      abi: [],
      functionName: 'symbol',
    });
  });

  it('should handle large LP token amounts correctly', () => {
    const mockBalance = BigInt('1000000000000000000000000'); // 1M LP tokens
    const mockTotalSupply = BigInt('100000000000000000000000000'); // 100M LP tokens

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: 'BlockCoop LP',
      })
      .mockReturnValueOnce({
        data: 'BCLP',
      });

    const { result } = renderHook(() => useLPToken());

    expect(result.current.balance).toBe('1000000');
    expect(result.current.totalSupply).toBe('100000000');
    expect(result.current.name).toBe('BlockCoop LP');
    expect(result.current.symbol).toBe('BCLP');
  });
});
