import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useOnChainPackages } from '../useOnChainPackages';

// Mock the dependencies
vi.mock('wagmi', () => ({
  useReadContract: vi.fn(),
}));

vi.mock('../usePackageManager', () => ({
  usePackageManager: vi.fn(),
}));

vi.mock('../../contracts', () => ({
  getVestingVaultContract: vi.fn(() => ({
    address: '0xVestingVault',
    abi: [],
  })),
}));

import { useReadContract } from 'wagmi';
import { usePackageManager } from '../usePackageManager';

const mockUseReadContract = useReadContract as any;
const mockUsePackageManager = usePackageManager as any;

describe('useOnChainPackages', () => {
  const mockGetPackageDetails = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    mockUsePackageManager.mockReturnValue({
      packageCount: 2,
      getPackageDetails: mockGetPackageDetails,
    });

    // Mock vesting configuration and package IDs - need to return objects with data property
    mockUseReadContract
      .mockReturnValueOnce({ data: BigInt(2592000) }) // defaultCliff: 30 days
      .mockReturnValueOnce({ data: BigInt(155520000) }) // defaultDuration: 5 years
      .mockReturnValueOnce({ data: [BigInt(0), BigInt(1)] }); // packageIds
  });

  it('should fetch and format packages correctly', async () => {
    // Mock package details
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('1000000000'), // 1000 USDT (6 decimals)
        tokenPriceBps: 1200, // 12% APY (correct field name from contract)
        vestSplitBps: 5000, // 50% vesting
        referralRateBps: 500, // 5% referral
        exists: true,
      })
      .mockResolvedValueOnce({
        entryUSDT: BigInt('5000000000'), // 5000 USDT
        tokenPriceBps: 1500, // 15% APY (correct field name from contract)
        vestSplitBps: 6000, // 60% vesting
        referralRateBps: 300, // 3% referral
        exists: true,
      });

    const { result } = renderHook(() => useOnChainPackages());

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.packages).toEqual([]);

    // Wait for packages to load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.packages).toHaveLength(2);
    expect(result.current.error).toBeNull();

    // Check first package
    const firstPackage = result.current.packages[0];
    expect(firstPackage.id).toBe('0');
    expect(firstPackage.name).toBe('Package 1');
    expect(firstPackage.minAmount).toBe(1000);
    expect(firstPackage.interestRate).toBe(12);
    expect(firstPackage.entryUSDT).toBe(BigInt('1000000000'));
    expect(firstPackage.priceBps).toBe(1200);
    expect(firstPackage.vestSplitBps).toBe(5000);
    expect(firstPackage.referralRateBps).toBe(500);
    expect(firstPackage.active).toBe(true);
  });

  it('should calculate vesting period from contract data', async () => {
    mockGetPackageDetails.mockResolvedValue({
      entryUSDT: BigInt('1000000000'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    });

    const { result } = renderHook(() => useOnChainPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should calculate approximately 60 months (5 years)
    expect(result.current.vestingPeriodMonths).toBeCloseTo(60, 0);
  });

  it('should handle package fetch errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    mockGetPackageDetails
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({
        entryUSDT: BigInt('1000000000'),
        tokenPriceBps: 1200, // Correct field name from contract
        vestSplitBps: 5000,
        referralRateBps: 500,
        exists: true,
      });

    const { result } = renderHook(() => useOnChainPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should have only one package (the successful one)
    expect(result.current.packages).toHaveLength(1);
    expect(result.current.error).toBeNull();
    expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching package 0:', expect.any(Error));
    
    consoleErrorSpy.mockRestore();
  });

  it('should filter out non-existent packages', async () => {
    mockGetPackageDetails
      .mockResolvedValueOnce({
        entryUSDT: BigInt('1000000000'),
        priceBps: 1200,
        vestSplitBps: 5000,
        referralRateBps: 500,
        exists: false, // This package doesn't exist
      })
      .mockResolvedValueOnce({
        entryUSDT: BigInt('5000000000'),
        priceBps: 1500,
        vestSplitBps: 6000,
        referralRateBps: 300,
        exists: true,
      });

    const { result } = renderHook(() => useOnChainPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should only have the existing package
    expect(result.current.packages).toHaveLength(1);
    expect(result.current.packages[0].id).toBe('1');
  });

  it('should handle zero package count', async () => {
    mockUsePackageManager.mockReturnValue({
      packageCount: 0,
      getPackageDetails: mockGetPackageDetails,
    });

    const { result } = renderHook(() => useOnChainPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.packages).toEqual([]);
    expect(result.current.error).toBeNull();
    expect(mockGetPackageDetails).not.toHaveBeenCalled();
  });

  it('should provide refetch functionality', async () => {
    mockGetPackageDetails.mockResolvedValue({
      entryUSDT: BigInt('1000000000'),
      priceBps: 1200,
      vestSplitBps: 5000,
      referralRateBps: 500,
      exists: true,
    });

    const { result } = renderHook(() => useOnChainPackages());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockGetPackageDetails).toHaveBeenCalledTimes(2); // Called for each package

    // Call refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockGetPackageDetails).toHaveBeenCalledTimes(4); // Called again
    });
  });
});
