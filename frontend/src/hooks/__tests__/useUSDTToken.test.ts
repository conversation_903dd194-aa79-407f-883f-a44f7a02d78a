import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useUSDTToken } from '../useUSDTToken';

// Mock wagmi hooks
vi.mock('wagmi', () => ({
  useAccount: vi.fn(),
  useReadContract: vi.fn(),
}));

// Mock viem utilities
vi.mock('viem', () => ({
  formatUnits: vi.fn((value: bigint, decimals: number) => {
    if (typeof value !== 'bigint') return '0'
    // Simple mock implementation for testing - just return a predictable value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    if (value === BigInt('**********')) return '1000' // 1000 USDT (6 decimals)
    if (value === BigInt('*********')) return '500' // 500 USDT
    if (value === BigInt('1000000')) return '1' // 1 USDT
    if (value === BigInt('500000')) return '0.5' // 0.5 USDT
    if (value === BigInt('**********000')) return '1000000' // 1M USDT
    if (value === BigInt('************')) return '999999.999999' // Large amount
    if (value === BigInt('***************')) return '*********' // Total supply
    return '0'
  }),
}));

// Mock contracts
vi.mock('../../contracts', () => ({
  getUSDTContract: vi.fn(() => ({
    address: '0xUSDTToken',
    abi: [],
  })),
  getPackageManagerContract: vi.fn(() => ({
    address: '0xPackageManager',
    abi: [],
  })),
}));

import { useAccount, useReadContract } from 'wagmi';

const mockUseAccount = useAccount as any;
const mockUseReadContract = useReadContract as any;

describe('useUSDTToken', () => {
  const mockAddress = '0x1234567890123456789012345678901234567890';

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseAccount.mockReturnValue({
      address: mockAddress,
    });
  });

  it('should return default values when no data is available', () => {
    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.allowance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
    expect(result.current.decimals).toBe(6);
    expect(typeof result.current.refetchBalance).toBe('function');
    expect(typeof result.current.refetchAllowance).toBe('function');
  });

  it('should format USDT balance with 6 decimals correctly', () => {
    const mockBalance = BigInt('**********'); // 1000 USDT (6 decimals)
    const mockAllowance = BigInt('*********'); // 500 USDT
    const mockTotalSupply = BigInt('***************'); // 100M USDT
    const mockDecimals = 6;

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockAllowance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: mockDecimals,
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('1000');
    expect(result.current.allowance).toBe('500');
    expect(result.current.totalSupply).toBe('*********');
    expect(result.current.decimals).toBe(6);
  });

  it('should handle small USDT amounts correctly', () => {
    const mockBalance = BigInt('1000000'); // 1 USDT (6 decimals)
    const mockAllowance = BigInt('500000'); // 0.5 USDT

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockAllowance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: BigInt('***************'),
      })
      .mockReturnValueOnce({
        data: 6,
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('1');
    expect(result.current.allowance).toBe('0.5');
  });

  it('should handle zero balances', () => {
    const mockBalance = BigInt('0');
    const mockAllowance = BigInt('0');
    const mockTotalSupply = BigInt('0');

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockAllowance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockTotalSupply,
      })
      .mockReturnValueOnce({
        data: 6,
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.allowance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
  });

  it('should handle undefined decimals with default value', () => {
    mockUseReadContract
      .mockReturnValueOnce({
        data: BigInt('**********'),
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: BigInt('*********'),
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: BigInt('***************'),
      })
      .mockReturnValueOnce({
        data: undefined, // No decimals data
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.decimals).toBe(6); // Should use default
    expect(result.current.balance).toBe('1000'); // Should still format correctly
  });

  it('should handle no connected address', () => {
    mockUseAccount.mockReturnValue({
      address: undefined,
    });

    mockUseReadContract.mockReturnValue({
      data: undefined,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('0');
    expect(result.current.allowance).toBe('0');
    expect(result.current.totalSupply).toBe('0');
  });

  it('should provide refetch functions', () => {
    const mockRefetchBalance = vi.fn();
    const mockRefetchAllowance = vi.fn();

    mockUseReadContract
      .mockReturnValueOnce({
        data: BigInt('**********'),
        refetch: mockRefetchBalance,
      })
      .mockReturnValueOnce({
        data: BigInt('*********'),
        refetch: mockRefetchAllowance,
      })
      .mockReturnValueOnce({
        data: BigInt('***************'),
      })
      .mockReturnValueOnce({
        data: 6,
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.refetchBalance).toBe(mockRefetchBalance);
    expect(result.current.refetchAllowance).toBe(mockRefetchAllowance);
  });

  it('should use correct contract calls', () => {
    mockUseReadContract.mockReturnValue({
      data: BigInt('**********'),
      refetch: vi.fn(),
    });

    renderHook(() => useUSDTToken());

    // Should call useReadContract 4 times for balance, allowance, totalSupply, decimals
    expect(mockUseReadContract).toHaveBeenCalledTimes(4);
    
    // Check balance call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(1, {
      address: '0xUSDTToken',
      abi: [],
      functionName: 'balanceOf',
      args: [mockAddress],
      query: {
        enabled: true,
      },
    });

    // Check allowance call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(2, {
      address: '0xUSDTToken',
      abi: [],
      functionName: 'allowance',
      args: [mockAddress, '0xPackageManager'],
      query: {
        enabled: true,
      },
    });

    // Check totalSupply call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(3, {
      address: '0xUSDTToken',
      abi: [],
      functionName: 'totalSupply',
    });

    // Check decimals call
    expect(mockUseReadContract).toHaveBeenNthCalledWith(4, {
      address: '0xUSDTToken',
      abi: [],
      functionName: 'decimals',
    });
  });

  it('should handle large USDT amounts correctly', () => {
    const mockBalance = BigInt('**********000'); // 1M USDT (6 decimals)
    const mockAllowance = BigInt('************'); // 999,999.999999 USDT

    mockUseReadContract
      .mockReturnValueOnce({
        data: mockBalance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: mockAllowance,
        refetch: vi.fn(),
      })
      .mockReturnValueOnce({
        data: BigInt('***************'),
      })
      .mockReturnValueOnce({
        data: 6,
      });

    const { result } = renderHook(() => useUSDTToken());

    expect(result.current.balance).toBe('1000000');
    expect(result.current.allowance).toBe('999999.999999');
  });
});
