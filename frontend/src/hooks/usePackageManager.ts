import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { useCallback } from 'react';
import { getPackageManagerContract, getUSDTContract } from '../contracts';
import { publicClient } from '../config/wagmi';

// Define the package details interface based on the contract structure
// Note: Field names must match the actual contract ABI
interface PackageDetails {
  entryUSDT: bigint;
  tokenPriceBps: number; // This is the actual field name from the contract
  vestSplitBps: number;
  referralRateBps: number;
  exists: boolean;
}

export const usePackageManager = () => {
  const { address } = useAccount();
  const { writeContractAsync, isPending, isSuccess, error } = useWriteContract();

  // Get available package IDs
  const { data: packageIds, error: packageIdsError } = useReadContract({
    ...getPackageManagerContract(),
    functionName: 'getPackageIds',
  });

  // Calculate package count from package IDs
  const packageCount = packageIds ? (packageIds as bigint[]).length : 0;



  /**
   * Get package details from the smart contract
   * Note: vestSplitBps represents the percentage of tokens allocated to vesting (e.g., 5000 = 50%)
   * This is NOT the vesting duration - all packages use a 5-year vesting period as defined in VestingVault
   */
  const getPackageDetails = useCallback(async (packageId: number): Promise<PackageDetails> => {
    const contract = getPackageManagerContract();
    if (!publicClient) {
      throw new Error('Public client is not initialized');
    }

    try {
      const result = await publicClient.readContract({
        address: contract.address,
        abi: contract.abi,
        functionName: 'getPackage',
        args: [packageId],
      });

      // Validate the result structure before returning
      if (!result || typeof result !== 'object') {
        throw new Error(`Invalid package data received for package ${packageId}`);
      }

      const packageData = result as any;

      // Validate required fields exist and have correct types
      if (typeof packageData.entryUSDT === 'undefined') {
        throw new Error(`Missing entryUSDT for package ${packageId}`);
      }
      if (typeof packageData.tokenPriceBps === 'undefined') {
        throw new Error(`Missing tokenPriceBps for package ${packageId}`);
      }
      if (typeof packageData.vestSplitBps === 'undefined') {
        throw new Error(`Missing vestSplitBps for package ${packageId}`);
      }
      if (typeof packageData.referralRateBps === 'undefined') {
        throw new Error(`Missing referralRateBps for package ${packageId}`);
      }
      if (typeof packageData.exists === 'undefined') {
        throw new Error(`Missing exists flag for package ${packageId}`);
      }

      // Return validated data with proper typing
      return {
        entryUSDT: BigInt(packageData.entryUSDT || 0),
        tokenPriceBps: Number(packageData.tokenPriceBps || 0),
        vestSplitBps: Number(packageData.vestSplitBps || 0),
        referralRateBps: Number(packageData.referralRateBps || 0),
        exists: Boolean(packageData.exists),
      } as PackageDetails;
    } catch (error) {
      console.error(`Error fetching package ${packageId}:`, error);
      throw error;
    }
  }, []); // Empty dependency array since this function doesn't depend on any reactive values
  
  // Purchase a package with proper approve flow
  const purchasePackage = useCallback(async (packageId: number, referrerAddress = '******************************************') => {
    if (!address) throw new Error('Wallet not connected');

    // 1. Get package details
    const packageDetails = await getPackageDetails(packageId);
    const entryAmount = packageDetails.entryUSDT;

    // 2. Always approve USDT first (simpler approach for now)
    // In production, you might want to check allowance first
    await writeContractAsync({
      ...getUSDTContract(),
      functionName: 'approve',
      args: [getPackageManagerContract().address, entryAmount],
    });

    // 3. Purchase the package
    return writeContractAsync({
      ...getPackageManagerContract(),
      functionName: 'purchase',
      args: [packageId, referrerAddress],
    });
  }, [address, getPackageDetails, writeContractAsync]);
  
  return {
    packageCount: packageCount || 0,
    getPackageDetails,
    purchasePackage,
    isPending,
    isSuccess,
    error,
    address
  };
};
