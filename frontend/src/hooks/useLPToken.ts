import { useAccount, useReadContract } from 'wagmi';
import { getLPTokenContract } from '../contracts';
import { formatEther } from 'viem';

export const useLPToken = () => {
  const { address } = useAccount();
  
  // Get LP token balance
  const { data: lpBalance, refetch: refetchLPBalance } = useReadContract({
    ...getLPTokenContract(),
    functionName: 'balanceOf',
    args: [address],
    query: {
      enabled: !!address,
    },
  });
  
  // Get LP token total supply
  const { data: lpTotalSupply } = useReadContract({
    ...getLPTokenContract(),
    functionName: 'totalSupply',
  });

  // Get LP token name
  const { data: lpTokenName } = useReadContract({
    ...getLPTokenContract(),
    functionName: 'name',
  });

  // Get LP token symbol
  const { data: lpTokenSymbol } = useReadContract({
    ...getLPTokenContract(),
    functionName: 'symbol',
  });
  
  return {
    balance: lpBalance ? formatEther(lpBalance) : '0',
    totalSupply: lpTotalSupply ? formatEther(lpTotalSupply) : '0',
    name: lpTokenName || 'LP Token',
    symbol: lpTokenSymbol || 'LP',
    refetchBalance: refetchLPBalance,
  };
};
