import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { usePackageManager } from './usePackageManager';
import { formatUnits } from 'viem';

export interface UserPackage {
  packageId: number;
  name: string;
  entryAmount: string;
  apy: number;
  vestingPercentage: number;
  referralRate: number;
  purchaseDate?: string;
  status: 'active' | 'completed';
}

export const useUserPackages = () => {
  const { address } = useAccount();
  const { packageCount, getPackageDetails } = usePackageManager();
  const [userPackages, setUserPackages] = useState<UserPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserPackages = async () => {
      if (!address || !packageCount) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const packages: UserPackage[] = [];
        
        // Fetch all available packages
        // Note: In a real implementation, you'd want to track which packages
        // a user has purchased through events or dedicated contract functions
        for (let i = 0; i < packageCount; i++) {
          try {
            const packageDetails = await getPackageDetails(i);
            
            // For demo purposes, assume user has purchased some packages
            // In production, you'd check purchase events or contract state
            const hasUserPurchased = i < 2; // Demo: user purchased first 2 packages
            
            if (hasUserPurchased) {
              packages.push({
                packageId: i,
                name: `Investment Package ${i + 1}`,
                entryAmount: formatUnits(packageDetails.entryUSDT, 6), // USDT has 6 decimals
                apy: packageDetails.priceBps / 100,
                vestingPercentage: packageDetails.vestSplitBps / 100,
                referralRate: packageDetails.referralRateBps / 100,
                purchaseDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                status: 'active',
              });
            }
          } catch (err) {
            console.error(`Error fetching package ${i}:`, err);
          }
        }
        
        setUserPackages(packages);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch user packages');
      } finally {
        setLoading(false);
      }
    };

    fetchUserPackages();
  }, [address, packageCount, getPackageDetails]);

  const totalInvested = userPackages.reduce(
    (sum, pkg) => sum + parseFloat(pkg.entryAmount), 
    0
  );

  const averageAPY = userPackages.length > 0 
    ? userPackages.reduce((sum, pkg) => sum + pkg.apy, 0) / userPackages.length 
    : 0;

  return {
    userPackages,
    totalInvested,
    averageAPY,
    packageCount: userPackages.length,
    loading,
    error,
    refetch: () => {
      // Trigger refetch
      setLoading(true);
      // The useEffect will handle the refetch
    },
  };
};
