import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { getVestingVaultContract } from '../contracts';
import { formatEther } from 'viem';

export const useVestingVault = () => {
  const { address } = useAccount();
  const { writeContractAsync, isPending, isSuccess, error } = useWriteContract();
  
  // Get vested amount
  const { data: vestedAmount, refetch: refetchVestedAmount } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'getVestedAmount',
    args: [address],
    query: {
      enabled: !!address,
    },
  });

  // Get claimable amount
  const { data: claimableAmount, refetch: refetchClaimableAmount } = useReadContract({
    ...getVestingVaultContract(),
    functionName: 'getClaimableAmount',
    args: [address],
    query: {
      enabled: !!address,
    },
  });
  
  // Claim vested tokens
  const claimVestedTokens = async () => {
    return writeContractAsync({
      ...getVestingVaultContract(),
      functionName: 'claim',
    });
  };
  
  return {
    vestedAmount: vestedAmount && typeof vestedAmount === 'bigint' ? formatEther(vestedAmount) : '0',
    claimableAmount: claimableAmount && typeof claimableAmount === 'bigint' ? formatEther(claimableAmount) : '0',
    claimVestedTokens,
    refetchVestedAmount,
    refetchClaimableAmount,
    isPending,
    isSuccess,
    error
  };
};