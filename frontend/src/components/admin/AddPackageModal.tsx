import React, { useState } from 'react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

interface AddPackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (packageData: {
    id: string;
    entryAmount: string;
    priceBps: string;
    vestSplitBps: string;
    referralRateBps: string;
  }) => Promise<void>;
  loading: boolean;
}

export const AddPackageModal: React.FC<AddPackageModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading
}) => {
  const [formData, setFormData] = useState({
    id: '',
    entryAmount: '',
    priceBps: '',
    vestSplitBps: '',
    referralRateBps: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.id) {
      newErrors.id = 'Package ID is required';
    } else if (parseInt(formData.id) <= 0) {
      newErrors.id = 'Package ID must be greater than 0';
    }

    if (!formData.entryAmount) {
      newErrors.entryAmount = 'Entry amount is required';
    } else if (parseFloat(formData.entryAmount) <= 0) {
      newErrors.entryAmount = 'Entry amount must be greater than 0';
    }

    if (!formData.priceBps) {
      newErrors.priceBps = 'Price (APY) is required';
    } else if (parseFloat(formData.priceBps) <= 0 || parseFloat(formData.priceBps) > 100) {
      newErrors.priceBps = 'Price must be between 0 and 100%';
    }

    if (!formData.vestSplitBps) {
      newErrors.vestSplitBps = 'Vesting split is required';
    } else if (parseFloat(formData.vestSplitBps) < 0 || parseFloat(formData.vestSplitBps) > 100) {
      newErrors.vestSplitBps = 'Vesting split must be between 0 and 100%';
    }

    if (!formData.referralRateBps) {
      newErrors.referralRateBps = 'Referral rate is required';
    } else if (parseFloat(formData.referralRateBps) < 0 || parseFloat(formData.referralRateBps) > 100) {
      newErrors.referralRateBps = 'Referral rate must be between 0 and 100%';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form on success
      setFormData({
        id: '',
        entryAmount: '',
        priceBps: '',
        vestSplitBps: '',
        referralRateBps: ''
      });
      setErrors({});
    } catch (error) {
      console.error('Error submitting package:', error);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        id: '',
        entryAmount: '',
        priceBps: '',
        vestSplitBps: '',
        referralRateBps: ''
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add New Package" size="md">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Package ID"
            type="number"
            value={formData.id}
            onChange={(e) => handleInputChange('id', e.target.value)}
            error={errors.id}
            helper="Unique identifier for the package"
            disabled={loading}
            required
          />
          
          <Input
            label="Entry Amount (USDT)"
            type="number"
            step="0.01"
            value={formData.entryAmount}
            onChange={(e) => handleInputChange('entryAmount', e.target.value)}
            error={errors.entryAmount}
            helper="Minimum investment amount in USDT"
            disabled={loading}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="APY (%)"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.priceBps}
            onChange={(e) => handleInputChange('priceBps', e.target.value)}
            error={errors.priceBps}
            helper="Annual percentage yield (e.g., 12 for 12% APY)"
            disabled={loading}
            required
          />

          <Input
            label="Vesting Split (%)"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.vestSplitBps}
            onChange={(e) => handleInputChange('vestSplitBps', e.target.value)}
            error={errors.vestSplitBps}
            helper="% of tokens locked for 5-year vesting (e.g., 50 = 50% vested, 50% immediate)"
            disabled={loading}
            required
          />

          <Input
            label="Referral Rate (%)"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.referralRateBps}
            onChange={(e) => handleInputChange('referralRateBps', e.target.value)}
            error={errors.referralRateBps}
            helper="Referral bonus percentage (e.g., 5 for 5% bonus)"
            disabled={loading}
            required
          />
        </div>

        <div className="bg-dark-50 p-4 rounded-lg">
          <h4 className="font-medium text-dark-900 mb-2">Package Preview</h4>
          <div className="space-y-1 text-sm text-dark-600">
            <p>Entry Amount: ${formData.entryAmount || '0'} USDT</p>
            <p>APY: {formData.priceBps || '0'}%</p>
            <p>Vesting: {formData.vestSplitBps || '0'}% of tokens will be vested</p>
            <p>Immediate: {formData.vestSplitBps ? (100 - parseFloat(formData.vestSplitBps)).toFixed(1) : '100'}% of tokens available immediately</p>
            <p>Referral Bonus: {formData.referralRateBps || '0'}%</p>
          </div>
          <div className="mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
            <p className="text-xs text-blue-700">
              <strong>Note:</strong> All packages use a 5-year vesting schedule as defined in the VestingVault contract.
              The vesting percentage determines how many tokens are locked for vesting vs. available immediately.
            </p>
          </div>
        </div>

        <div className="flex space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            disabled={loading}
            className="flex-1"
          >
            {loading ? 'Adding Package...' : 'Add Package'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
